import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Card, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';
import { ExternalLink, Github, Calendar, Code, Zap } from 'lucide-react';
import Image from 'next/image';

interface Project {
  id: number;
  title: string;
  description: string;
  image: string;
  tags: string[];
  demoUrl: string;
  repoUrl: string;
  category: string;
  year: string;
  status: 'completed' | 'in-progress';
}

const projectsData: Project[] = [
  {
    id: 1,
    title: 'Event Management System',
    description: 'A comprehensive full-stack web application for event planning and management. Features event creation, user registration, real-time updates, and administrative dashboard.',
    image: 'https://images.pexels.com/photos/39284/macbook-apple-imac-computer-39284.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    tags: ['React', 'Next.js', 'Tailwind CSS', 'MongoDB', 'Node.js'],
    demoUrl: 'https://event-management-sage-iota.vercel.app/',
    repoUrl: 'https://github.com/Harishrawal2/Employee-Management-System.',
    category: 'Full Stack',
    year: '2024',
    status: 'completed'
  },
  {
    id: 2,
    title: 'Food Delivery Backend',
    description: 'Robust backend API for food delivery platform with order management, user authentication, restaurant integration, and real-time tracking capabilities.',
    image: 'https://images.pexels.com/photos/1181263/pexels-photo-1181263.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    tags: ['Node.js', 'Express', 'MongoDB', 'JWT', 'Nodemailer', 'bcrypt'],
    demoUrl: 'https://github.com/Harishrawal2/nodejs_course/tree/food_delivery_Back-end',
    repoUrl: 'https://github.com/Harishrawal2/nodejs_course/tree/food_delivery_Back-end',
    category: 'Backend',
    year: '2024',
    status: 'completed'
  },
  {
    id: 3,
    title: 'Twitter Clone Backend',
    description: 'Scalable social media backend with user authentication, tweet management, following system, and real-time notifications using modern technologies.',
    image: 'https://images.pexels.com/photos/1181676/pexels-photo-1181676.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    tags: ['Node.js', 'Express.js', 'MongoDB', 'Socket.io', 'JWT', 'Mongoose'],
    demoUrl: 'https://github.com/Harishrawal2/tweeterclone_mern',
    repoUrl: 'https://github.com/Harishrawal2/tweeterclone_mern',
    category: 'Backend',
    year: '2023',
    status: 'completed'
  },
  {
    id: 4,
    title: 'Interactive Landing Page',
    description: 'Modern landing page with 3D animations, interactive elements, and responsive design. Built with cutting-edge web technologies for optimal user experience.',
    image: 'https://images.pexels.com/photos/7988079/pexels-photo-7988079.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    tags: ['React', 'Three.js', 'Framer Motion', 'Tailwind CSS'],
    demoUrl: 'https://github.com/Harishrawal2/Landing_page_website',
    repoUrl: 'https://github.com/Harishrawal2/Landing_page_website',
    category: 'Frontend',
    year: '2023',
    status: 'completed'
  },
];

const techEmojis = [
  '💻', '🍔', '🐦', '🚀', '🎨', '📱', '🛒', '🔒', '🌐', '⚡'
];

const ProjectCard: React.FC<{ project: Project; index: number }> = ({ project, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ delay: index * 0.1, duration: 0.6, type: 'spring' }}
      className="group relative"
      whileHover={{ scale: 1.04, rotate: [0, 1, -1, 0] }}
    >
      {/* Floating Tech Emoji/Icon */}
      <motion.div
        className="absolute -top-6 left-1/2 -translate-x-1/2 z-20 text-3xl drop-shadow-lg pointer-events-none select-none animate-float"
        animate={{ y: [0, -10, 0] }}
        transition={{ duration: 3 + index * 0.2, repeat: Infinity, ease: 'easeInOut' }}
      >
        {techEmojis[index % techEmojis.length]}
      </motion.div>
      <Card className="overflow-hidden bg-white/10 backdrop-blur-xl border-2 border-white/10 group-hover:border-primary-500/60 transition-all duration-500 hover:shadow-2xl hover:shadow-primary-500/20 group-hover:scale-[1.03] relative">
        {/* Image Section with Animated Overlay */}
        <div className="relative h-56 overflow-hidden">
          <Image
            src={project.image}
            alt={project.title}
            width={500}
            height={300}
            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
          />
          {/* Animated Overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-primary-500/20 via-secondary-500/20 to-pink-400/20 opacity-0 group-hover:opacity-80 transition-all duration-500 pointer-events-none"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 0.8 }}
          />
          {/* Status Badge */}
          <div className="absolute top-4 left-4 z-10">
            <div className={`px-3 py-1.5 rounded-full text-xs font-medium backdrop-blur-sm transition-all duration-300 animate-pulse shadow-lg shadow-green-500/10 border ${
              project.status === 'completed'
                ? 'bg-green-500/30 text-green-300 border-green-400/50'
                : 'bg-yellow-500/30 text-yellow-300 border-yellow-400/50'
            }`}>
              {project.status === 'completed' ? '✅ Completed' : '🚧 In Progress'}
            </div>
          </div>
          {/* Category Badge */}
          <div className="absolute top-4 right-4 z-10">
            <div className="px-3 py-1.5 rounded-full text-xs font-medium bg-primary-500/30 text-primary-300 border border-primary-400/50 backdrop-blur-sm shadow-lg shadow-primary-500/20 animate-float">
              {project.category}
            </div>
          </div>
          {/* Enhanced Hover Content Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/70 to-black/40 opacity-0 group-hover:opacity-100 transition-all duration-500 flex flex-col justify-center items-center p-6 text-center">
            {/* Project Title */}
            <motion.h3
              className="text-xl font-bold text-white mb-3 drop-shadow-lg"
              initial={{ y: 20, opacity: 0 }}
              whileHover={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              {project.title}
            </motion.h3>
            {/* Project Description */}
            <motion.p
              className="text-white/90 text-sm leading-relaxed mb-4 line-clamp-3 max-w-xs"
              initial={{ y: 20, opacity: 0 }}
              whileHover={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {project.description}
            </motion.p>
            {/* Key Technologies */}
            <motion.div
              className="flex flex-wrap gap-1 mb-4 justify-center max-w-xs"
              initial={{ y: 20, opacity: 0 }}
              whileHover={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {project.tags.slice(0, 4).map((tag, idx) => (
                <Badge
                  key={idx}
                  className="text-xs bg-white/20 text-white border-white/30 hover:bg-white/30 transition-colors animate-pulse"
                >
                  {tag}
                </Badge>
              ))}
              {project.tags.length > 4 && (
                <Badge className="text-xs bg-primary-500/30 text-primary-200 border-primary-400/50 animate-bounce">
                  +{project.tags.length - 4}
                </Badge>
              )}
            </motion.div>
            {/* Action Buttons */}
            <motion.div
              className="flex gap-3"
              initial={{ y: 20, opacity: 0 }}
              whileHover={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              <Button
                size="sm"
                variant="outline"
                className="bg-white/10 backdrop-blur-md border-white/30 text-white hover:bg-white/20 hover:scale-110 transition-all duration-300 animate-float"
                onClick={() => window.open(project.repoUrl, '_blank')}
              >
                <Github size={16} className="mr-2" />
                Code
              </Button>
              <Button
                size="sm"
                className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white hover:from-primary-400 hover:to-secondary-400 hover:scale-110 transition-all duration-300 shadow-lg animate-float"
                onClick={() => window.open(project.demoUrl, '_blank')}
              >
                <ExternalLink size={16} className="mr-2" />
                Live Demo
              </Button>
            </motion.div>
          </div>
          {/* Animated Border Effect */}
          <div className="absolute inset-0 border-2 border-transparent group-hover:border-primary-500/70 transition-all duration-500 rounded-lg pointer-events-none"></div>
        </div>
        <CardContent className="p-6 space-y-4">
          {/* Header */}
          <div className="space-y-3">
            <h3 className="text-xl font-bold text-foreground group-hover:text-primary-400 transition-colors duration-300">
              {project.title}
            </h3>
            <div className="flex items-center gap-4 text-sm text-foreground/60">
              <div className="flex items-center gap-1.5">
                <Calendar size={14} className="text-primary-400" />
                <span>{project.year}</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Code size={14} className="text-secondary-400" />
                <span>{project.tags.length} Technologies</span>
              </div>
            </div>
          </div>
          {/* Description */}
          <p className="text-foreground/70 leading-relaxed text-sm line-clamp-3">
            {project.description}
          </p>
          {/* Technologies */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-foreground/80">Tech Stack:</h4>
            <div className="flex flex-wrap gap-2">
              {project.tags.map((tag, idx) => (
                <Badge
                  key={idx}
                  variant={idx % 2 === 0 ? 'default' : 'secondary'}
                  className="text-xs hover:scale-105 transition-all duration-200 hover:shadow-md"
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

const Projects: React.FC = () => {
  const [isHovered, setIsHovered] = React.useState(false);

  return (
    <Section id="projects" className="bg-background relative overflow-hidden">
      {/* Clean background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background to-background"></div>
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-secondary-500/5 rounded-full blur-3xl"></div>
      </div>

      <SectionTitle
        title="Featured Projects"
        subtitle="Showcasing innovative solutions built with modern technologies"
        centered
      />

      {/* Infinite Projects Slider */}
      <div
        className="relative overflow-hidden"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <motion.div
          className="flex gap-8"
          animate={{
            x: isHovered ? undefined : [0, -100 * projectsData.length],
          }}
          transition={{
            x: {
              repeat: Infinity,
              repeatType: "loop",
              duration: projectsData.length * 10,
              ease: "linear",
            },
          }}
          style={{
            width: `${(projectsData.length * 2) * 100}%`,
          }}
        >
          {/* First set of projects */}
          {projectsData.map((project, index) => (
            <div key={`first-${project.id}`} className="flex-shrink-0 w-96">
              <ProjectCard
                project={project}
                index={index}
              />
            </div>
          ))}

          {/* Duplicate set for seamless loop */}
          {projectsData.map((project, index) => (
            <div key={`second-${project.id}`} className="flex-shrink-0 w-96">
              <ProjectCard
                project={project}
                index={index + projectsData.length}
              />
            </div>
          ))}
        </motion.div>

        {/* Gradient overlays for smooth edges */}
        <div className="absolute inset-y-0 left-0 w-32 bg-gradient-to-r from-background to-transparent pointer-events-none z-10"></div>
        <div className="absolute inset-y-0 right-0 w-32 bg-gradient-to-l from-background to-transparent pointer-events-none z-10"></div>

        {/* Slider Status Indicator */}
        <div className="absolute top-4 right-4 z-20">
          <motion.div
            className="flex items-center gap-2 px-3 py-1.5 bg-foreground/10 backdrop-blur-sm rounded-full border border-foreground/20"
            animate={{
              scale: isHovered ? 1.05 : 1,
            }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              className="w-2 h-2 rounded-full"
              animate={{
                backgroundColor: isHovered ? "#ef4444" : "#10b981",
              }}
              transition={{ duration: 0.3 }}
            />
            <span className="text-xs text-foreground/70 font-medium">
              {isHovered ? "Paused" : "Auto-scroll"}
            </span>
          </motion.div>
        </div>
      </div>

      {/* Single CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ delay: 0.4 }}
        className="mt-12 text-center space-y-4"
      >
        <p className="text-sm text-foreground/60">
          💡 Hover over project cards to see details and actions
        </p>

        <Button
          className="bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-400 hover:to-secondary-400 text-white px-8 py-3 rounded-full shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
          onClick={() => window.open('https://github.com', '_blank')}
        >
          <Github size={18} className="mr-2" />
          View All Projects on GitHub
          <ExternalLink size={16} className="ml-2" />
        </Button>
      </motion.div>
    </Section>
  );
};

export default Projects;

/* Add this to your global CSS (e.g., globals.css) for the creative project cards:
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}
.animate-float {
  animation: float 4s ease-in-out infinite;
}
*/