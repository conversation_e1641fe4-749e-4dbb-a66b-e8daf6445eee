import React from 'react';
import { Container } from './ui/Container';
import { Github, Linkedin, Twitter, Mail, SquareCode } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-background border-t border-foreground/10">
      <Container>
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-1">
              <Link href="#home" className="flex gap-3 items-center font-bold text-gradient">
                <Image
                  src="/logo.png"
                  alt="Logo"
                  width={30}
                  height={30}
                  className="object-contain"
                />
                HarishRawal
              </Link>
              <p className="mt-4 text-foreground/60">
                Creating beautiful digital experiences with code and creativity.
              </p>
              <div className="flex space-x-4 mt-6">
                <Link
                  href="https://github.com/Harishrawal2"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-foreground/60 hover:text-foreground transition-colors"
                  aria-label="GitHub"
                >
                  <Github size={20} />
                </Link>
                <Link
                  href="https://www.linkedin.com/in/harish-rawal-b4024b211/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-foreground/60 hover:text-foreground transition-colors"
                  aria-label="LinkedIn"
                >
                  <Linkedin size={20} />
                </Link>
                <Link
                  href="https://leetcode.com/u/harish8126/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-foreground/60 hover:text-foreground transition-colors"
                  aria-label="SquareCode"
                >
                  <SquareCode size={20} />
                </Link>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-foreground/60 hover:text-foreground transition-colors"
                  aria-label="Email"
                >
                  <Mail size={20} />
                </Link>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider text-foreground/70 mb-4">
                Links
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="#home"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    href="#about"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    About
                  </Link>
                </li>
                <li>
                  <Link
                    href="#projects"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Projects
                  </Link>
                </li>
                <li>
                  <Link
                    href="#skills"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Skills
                  </Link>
                </li>
                <li>
                  <Link
                    href="#contact"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider text-foreground/70 mb-4">
                Services
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="#"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Web Development
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Frontend Development
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Backend Development
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Full-stack Development
                  </Link>
                </li>
                <li>
                  <Link
                    href="#"
                    className="text-foreground/60 hover:text-foreground transition-colors"
                  >
                    Next.js Development
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold uppercase tracking-wider text-foreground/70 mb-4">
                Contact
              </h3>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <Mail size={16} className="mt-1 mr-2 text-foreground/50" />
                  <span className="text-foreground/60"><EMAIL></span>
                </li>
                <li className="flex items-start">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mt-1 mr-2 text-foreground/50"
                  >
                    <path d="M22 16.92v3a2 2 0 01-2.18 2 19.79 19.79 0 01-8.63-3.07 19.5 19.5 0 01-6-6 19.79 19.79 0 01-3.07-8.67A2 2 0 014.11 2h3a2 2 0 012 1.72 12.84 12.84 0 00.7 2.81 2 2 0 01-.45 2.11L8.09 9.91a16 16 0 006 6l1.27-1.27a2 2 0 012.11-.45 12.84 12.84 0 002.81.7A2 2 0 0122 16.92z" />
                  </svg>
                  <span className="text-foreground/60">+91 7817837369</span>
                </li>
                <li className="flex items-start">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mt-1 mr-2 text-foreground/50"
                  >
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" />
                    <circle cx="12" cy="10" r="3" />
                  </svg>
                  <span className="text-foreground/60">Motera, Ahmedabad, Gujarat</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="py-4 border-t border-foreground/10 flex flex-col md:flex-row justify-between items-center">
          <div className="text-foreground/60 text-sm">
            © {currentYear} HarishRawal. All rights reserved.
          </div>
          <div className="mt-4 md:mt-0 text-foreground/60 text-sm">
            <span>Designed and developed with ❤️</span>
          </div>
        </div>
      </Container>
    </footer>
  );
};

export default Footer;