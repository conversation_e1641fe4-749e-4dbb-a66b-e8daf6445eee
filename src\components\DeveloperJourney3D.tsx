'use client';

import React, { useState, useEffect, useRef, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Text, Box, Sphere, Html } from '@react-three/drei';
import { motion } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Button } from './ui/Button';
import { Play, Pause, RotateCcw } from 'lucide-react';
import * as THREE from 'three';

// Story state management
interface StoryState {
  currentScene: number;
  isPlaying: boolean;
  progress: number;
}

// Scene data
interface Scene {
  id: number;
  title: string;
  location: string;
  dialogue: string;
  narration: string;
  character: string;
  background: string;
  props: string[];
}

// Scene data
const scenes: Scene[] = [
  {
    id: 1,
    title: "College Entrance",
    location: "FIET College Gate, Bareilly",
    dialogue: "<PERSON>, Computer Engineering lena hai! Coding seekhni hai!",
    narration: "<PERSON> stands at the college gate, excited about his future in technology...",
    character: "🧑‍🎓",
    background: "🏫",
    props: ["📚", "🎒", "🚪"]
  },
  {
    id: 2,
    title: "First Day in Class",
    location: "Computer Lab - Programming Class",
    dialogue: "Yeh computer lab hai! C programming seekhna hai!",
    narration: "<PERSON>sh enters the computer lab with his classmates, ready to learn programming...",
    character: "👨‍💻",
    background: "🏫",
    props: ["💻", "👨‍🏫", "👥", "📖"]
  },
  {
    id: 3,
    title: "Learning Documentation",
    location: "Library - Study Session",
    dialogue: "MDN Web Docs, W3Schools - HTML CSS seekhna hai!",
    narration: "Late night study sessions with documentation and tutorials...",
    character: "📚",
    background: "📖",
    props: ["💻", "📚", "☕", "🌙"]
  },
  {
    id: 4,
    title: "Practice Coding",
    location: "Hostel Room - Coding Practice",
    dialogue: "Hello World! Mera pehla program chal gaya!",
    narration: "Hours of practice, debugging, and learning from mistakes...",
    character: "👨‍💻",
    background: "🏠",
    props: ["💻", "🖥️", "📱", "⌨️"]
  },
  {
    id: 5,
    title: "Internship Hunt",
    location: "Online Job Portals",
    dialogue: "LinkedIn, Naukri, Internshala - kahan apply karu?",
    narration: "The search for the first internship begins...",
    character: "🔍",
    background: "💼",
    props: ["💻", "📧", "📄", "🌐"]
  },
  {
    id: 6,
    title: "First Internship",
    location: "Zafron Technology - Remote Work",
    dialogue: "Mera pehla internship! Food delivery app banana hai!",
    narration: "First real project - building a full-stack application...",
    character: "🎉",
    background: "🏢",
    props: ["💻", "📱", "🍕", "⚡"]
  },
  {
    id: 7,
    title: "Eyefounder Job",
    location: "Eyefounder Office, Budaun",
    dialogue: "Office environment, team meetings, real clients!",
    narration: "Professional life begins with real client projects...",
    character: "👔",
    background: "🏢",
    props: ["💻", "👥", "📊", "☕"]
  },
  {
    id: 8,
    title: "Serviots Journey",
    location: "Serviots Office, Ahmedabad",
    dialogue: "Enterprise solutions, scalable systems banane ka time!",
    narration: "Moving to the big city for bigger challenges...",
    character: "🚀",
    background: "🏙️",
    props: ["💻", "🏢", "⚡", "🌟"]
  },
  {
    id: 9,
    title: "Continuous Learning",
    location: "Always Growing",
    dialogue: "AI, ML, aur naye technologies - seekhna kabhi band nahi hota!",
    narration: "The journey continues with new technologies and challenges...",
    character: "🧠",
    background: "🌟",
    props: ["💻", "🤖", "📚", "🚀"]
  }
];

// 3D Character Component
const Character3D: React.FC<{
  position: [number, number, number];
  action: 'idle' | 'walking' | 'sitting' | 'typing' | 'teaching';
  color?: string;
  scale?: number;
}> = ({ position, action, color = "#FDBCB4", scale = 1 }) => {
  const meshRef = useRef<THREE.Group>(null);

  useFrame((state) => {
    if (meshRef.current) {
      // Breathing animation
      meshRef.current.scale.y = scale + Math.sin(state.clock.elapsedTime * 2) * 0.05;

      // Action-specific animations
      switch (action) {
        case 'walking':
          meshRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 8) * 0.1;
          meshRef.current.position.x = position[0] + Math.sin(state.clock.elapsedTime * 4) * 0.1;
          break;
        case 'typing':
          meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 10) * 0.05;
          break;
        case 'teaching':
          meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 3) * 0.2;
          break;
      }
    }
  });

  return (
    <group ref={meshRef} position={position} scale={scale * 2}>
      {/* Character Body - Bigger and more colorful */}
      <Box position={[0, 0.8, 0]} args={[0.8, 2.4, 0.6]}>
        <meshStandardMaterial color="#FF6B6B" />
      </Box>

      {/* Character Head - Much bigger */}
      <Box position={[0, 1.6, 0]} args={[1.0, 1.0, 0.8]}>
        <meshStandardMaterial color={color} />
      </Box>

      {/* Eyes - Bigger and more visible */}
      <Box position={[-0.2, 1.8, 0.41]} args={[0.16, 0.16, 0.04]}>
        <meshStandardMaterial color="#000" />
      </Box>
      <Box position={[0.2, 1.8, 0.41]} args={[0.16, 0.16, 0.04]}>
        <meshStandardMaterial color="#000" />
      </Box>

      {/* Smile */}
      <Box position={[0, 1.5, 0.41]} args={[0.3, 0.05, 0.02]}>
        <meshStandardMaterial color="#000" />
      </Box>

      {/* Hair - More prominent */}
      <Box position={[0, 2.2, 0]} args={[1.2, 0.4, 1.0]}>
        <meshStandardMaterial color="#2C1810" />
      </Box>

      {/* Arms - Bigger and animated */}
      <Box
        position={[-0.7, 1, 0]}
        rotation={[0, 0, action === 'typing' || action === 'teaching' ? -0.3 : 0]}
        args={[0.3, 1.2, 0.3]}
      >
        <meshStandardMaterial color={color} />
      </Box>
      <Box
        position={[0.7, 1, 0]}
        rotation={[0, 0, action === 'typing' || action === 'teaching' ? 0.3 : 0]}
        args={[0.3, 1.2, 0.3]}
      >
        <meshStandardMaterial color={color} />
      </Box>

      {/* Legs - Bigger */}
      <Box position={[-0.3, 0.2, 0]} args={[0.3, 1.2, 0.3]}>
        <meshStandardMaterial color="#2C3E50" />
      </Box>
      <Box position={[0.3, 0.2, 0]} args={[0.3, 1.2, 0.3]}>
        <meshStandardMaterial color="#2C3E50" />
      </Box>

      {/* Feet */}
      <Box position={[-0.3, -0.3, 0.2]} args={[0.4, 0.2, 0.6]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      <Box position={[0.3, -0.3, 0.2]} args={[0.4, 0.2, 0.6]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
    </group>
  );
};

// 3D Laptop Component
const Laptop3D: React.FC<{
  position: [number, number, number];
  isOpen?: boolean;
  showCode?: boolean;
}> = ({ position, isOpen = true, showCode = true }) => {
  return (
    <group position={position}>
      {/* Laptop Base - Much bigger */}
      <Box position={[0, 0, 0]} args={[3, 0.2, 2]}>
        <meshStandardMaterial color="#2C3E50" />
      </Box>

      {/* Laptop Screen - Much bigger */}
      <Box
        position={[0, 1, -0.8]}
        rotation={[isOpen ? -Math.PI / 4 : 0, 0, 0]}
        args={[2.8, 1.8, 0.1]}
      >
        <meshStandardMaterial color="#000" />
      </Box>

      {/* Screen Content - Bigger and more visible */}
      {isOpen && showCode && (
        <Text
          position={[0, 1, -0.7]}
          rotation={[-Math.PI / 4, 0, 0]}
          fontSize={0.2}
          color="#00FF00"
          anchorX="center"
          anchorY="middle"
        >
          {`<h1>Hello World!</h1>\nconsole.log("Coding!");\nfunction learn() {\n  return "Success";\n}`}
        </Text>
      )}

      {/* Keyboard */}
      <Box position={[0, 0.1, 0.3]} args={[2.5, 0.05, 1]}>
        <meshStandardMaterial color="#333" />
      </Box>
    </group>
  );
};

// 3D Classroom Environment
const Classroom3D: React.FC<{ showTeacher?: boolean; showStudents?: boolean }> = ({
  showTeacher = false,
  showStudents = false
}) => {
  return (
    <group>
      {/* Floor - More visible */}
      <Box position={[0, -1, 0]} args={[30, 0.5, 30]}>
        <meshStandardMaterial color="#D2B48C" />
      </Box>

      {/* Walls - Bigger */}
      <Box position={[0, 4, -12]} args={[30, 10, 0.5]}>
        <meshStandardMaterial color="#F0F8FF" />
      </Box>

      {/* Blackboard - Much bigger */}
      <Box position={[0, 3, -11.5]} args={[12, 6, 0.2]}>
        <meshStandardMaterial color="#2F4F2F" />
      </Box>

      {/* Code on Blackboard - Bigger text */}
      <Text
        position={[0, 3, -11.3]}
        fontSize={0.6}
        color="#FFFFFF"
        anchorX="center"
        anchorY="middle"
      >
        {`HTML Basics\n\n<html>\n  <body>\n    <h1>Hello World!</h1>\n  </body>\n</html>`}
      </Text>

      {/* Teacher - Much bigger and more visible */}
      {showTeacher && (
        <Character3D
          position={[0, 0, -9]}
          action="teaching"
          color="#F4C2A1"
          scale={1.5}
        />
      )}

      {/* Student Desks - Bigger */}
      {[-6, -3, 0, 3, 6].map((x, i) => (
        <group key={i}>
          <Box position={[x, 0.6, 4]} args={[2, 0.2, 1.5]}>
            <meshStandardMaterial color="#8B4513" />
          </Box>
          <Box position={[x, 0.6, 7]} args={[2, 0.2, 1.5]}>
            <meshStandardMaterial color="#8B4513" />
          </Box>
        </group>
      ))}

      {/* Students - Much bigger and more visible */}
      {showStudents && (
        <>
          <Character3D position={[-6, 0, 3]} action="sitting" color="#F4C2A1" scale={1.2} />
          <Character3D position={[-3, 0, 3]} action="sitting" color="#D4A574" scale={1.2} />
          <Character3D position={[0, 0, 3]} action="sitting" color="#E8B896" scale={1.2} />
          <Character3D position={[3, 0, 3]} action="sitting" color="#F2D2BD" scale={1.2} />
          <Character3D position={[6, 0, 3]} action="sitting" color="#C8956D" scale={1.2} />

          <Character3D position={[-6, 0, 6]} action="sitting" color="#E0B088" scale={1.2} />
          <Character3D position={[-3, 0, 6]} action="sitting" color="#F5D5C0" scale={1.2} />
          <Character3D position={[0, 0, 6]} action="sitting" color="#DDB892" scale={1.2} />
          <Character3D position={[3, 0, 6]} action="sitting" color="#F4C2A1" scale={1.2} />
          <Character3D position={[6, 0, 6]} action="sitting" color="#D4A574" scale={1.2} />
        </>
      )}
    </group>
  );
};

// 3D Office Environment
const Office3D: React.FC<{ company: string }> = ({ company }) => {
  return (
    <group>
      {/* Floor */}
      <Box position={[0, -0.1, 0]} args={[20, 0.2, 20]}>
        <meshStandardMaterial color="#F5F5F5" />
      </Box>

      {/* Walls */}
      <Box position={[0, 3, -10]} args={[20, 6, 0.2]}>
        <meshStandardMaterial color="#FFFFFF" />
      </Box>

      {/* Company Sign */}
      <Text
        position={[0, 4, -9.9]}
        fontSize={0.5}
        color={company === 'Eyefounder' ? "#FF6B6B" : "#4ECDC4"}
        anchorX="center"
        anchorY="middle"
      >
        {company}
      </Text>

      {/* Office Desks */}
      {[-4, -1, 2, 5].map((x, i) => (
        <group key={i}>
          <Box position={[x, 0.4, 2]} args={[2, 0.1, 1.2]}>
            <meshStandardMaterial color="#FFFFFF" />
          </Box>
          {/* Monitor */}
          <Box position={[x, 0.8, 1.5]} args={[0.8, 0.5, 0.05]}>
            <meshStandardMaterial color="#000" />
          </Box>
        </group>
      ))}

      {/* Office Workers */}
      <Character3D position={[-4, 0, 1]} action="typing" color="#F4C2A1" />
      <Character3D position={[-1, 0, 1]} action="typing" color="#D4A574" />
      <Character3D position={[2, 0, 1]} action="typing" color="#E8B896" />
      <Character3D position={[5, 0, 1]} action="typing" color="#F2D2BD" />
    </group>
  );
};











// Main Component
const DeveloperJourney3D: React.FC = () => {
  const [currentScene, setCurrentScene] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);

  const SCENE_DURATION = 8000; // 8 seconds per scene

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying) {
      const interval = setInterval(() => {
        setCurrentScene((prev) => {
          if (prev >= scenes.length - 1) {
            setIsPlaying(false);
            return 0;
          }
          return prev + 1;
        });
      }, SCENE_DURATION);
      return () => clearInterval(interval);
    }
  }, [isPlaying, currentScene]);

  // Progress bar animation
  useEffect(() => {
    if (isPlaying) {
      setProgress(0);
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            return 0;
          }
          return prev + (100 / (SCENE_DURATION / 100));
        });
      }, 100);
      return () => clearInterval(progressInterval);
    }
  }, [isPlaying, currentScene]);

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    setCurrentScene(0);
    setProgress(0);
    setIsPlaying(true);
  };

  const scene = scenes[currentScene];

  return (
    <Section id="journey-3d" className="bg-dark-900 relative min-h-screen">
      <SectionTitle
        title="🎬 Complete Developer Journey - 3D Interactive Story"
        subtitle="3D cartoon story with characters: College → Learning → Internship → Eyefounder → Serviots → Continuous Growth"
        centered
      />

      {/* 3D Theater */}
      <div className="max-w-6xl mx-auto">
        {/* Main 3D Stage */}
        <div className="relative h-[600px] bg-gradient-to-br from-sky-200 to-blue-400 rounded-xl overflow-hidden border-4 border-gray-800 shadow-2xl">
          <Canvas camera={{ position: [0, 8, 15], fov: 75 }}>
            <Suspense fallback={null}>
              {/* Lighting - Much brighter */}
              <ambientLight intensity={1.2} />
              <directionalLight position={[10, 10, 5]} intensity={2} />
              <directionalLight position={[-10, 10, 5]} intensity={1.5} />
              <pointLight position={[0, 8, 5]} intensity={1.5} />
              <pointLight position={[5, 5, 5]} intensity={1} />
              <pointLight position={[-5, 5, 5]} intensity={1} />

              {/* Scene Content */}
              {currentScene === 0 && (
                <group>
                  {/* College Building - Much bigger */}
                  <Box position={[0, 4, -12]} args={[12, 8, 2]}>
                    <meshStandardMaterial color="#8B4513" />
                  </Box>
                  <Text position={[0, 6, -11]} fontSize={1} color="#FFFFFF">
                    FIET COLLEGE
                  </Text>
                  {/* Main Character - Much bigger and closer */}
                  <Character3D position={[0, 0, 5]} action="idle" scale={2} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 1 && (
                <Classroom3D showTeacher={true} showStudents={true} />
              )}

              {currentScene === 2 && (
                <group>
                  <Classroom3D showTeacher={false} showStudents={false} />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 3 && (
                <group>
                  <Classroom3D showTeacher={false} showStudents={false} />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 4 && (
                <group>
                  <Classroom3D showTeacher={false} showStudents={false} />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 5 && (
                <group>
                  <Office3D company="Zafron Technology" />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 6 && (
                <group>
                  <Office3D company="Eyefounder" />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 7 && (
                <group>
                  <Office3D company="Serviots" />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {currentScene === 8 && (
                <group>
                  <Office3D company="Serviots" />
                  <Laptop3D position={[0, 1, 2]} isOpen={true} showCode={true} />
                  <Character3D position={[0, 0, 4]} action="typing" scale={1.8} color="#FDBCB4" />
                </group>
              )}

              {/* Interactive UI Elements */}
              <Html position={[0, 4, 0]}>
                <div className="bg-black/80 text-white px-4 py-2 rounded-lg text-center">
                  <h3 className="text-lg font-bold">Scene {currentScene + 1}: {scene.title}</h3>
                  <p className="text-sm text-gray-300">{scene.location}</p>
                </div>
              </Html>

              <Html position={[0, -2, 0]}>
                <div className="bg-black/80 text-white p-4 rounded-lg max-w-md text-center">
                  <div className="flex items-center gap-2 mb-2 justify-center">
                    <span className="text-yellow-400">💬</span>
                    <span className="font-bold">"{scene.dialogue}"</span>
                  </div>
                  <p className="text-sm italic text-gray-300">{scene.narration}</p>
                </div>
              </Html>

              {/* Camera Controls */}
              <OrbitControls
                enablePan={false}
                enableZoom={true}
                enableRotate={true}
                maxDistance={15}
                minDistance={5}
              />
            </Suspense>
          </Canvas>

          {/* Progress Bar */}
          <div className="absolute bottom-0 left-0 right-0 h-2 bg-black/50">
            <motion.div
              className="h-full bg-gradient-to-r from-green-500 to-blue-500"
              style={{ width: `${progress}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
        </div>

        {/* Cartoon Controls */}
        <div className="mt-6 bg-gray-800 rounded-xl p-6">
          <div className="flex items-center justify-between">
            {/* Scene Navigation */}
            <div className="flex gap-1">
              {scenes.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentScene(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    index === currentScene
                      ? 'bg-blue-500 scale-125'
                      : index < currentScene
                      ? 'bg-green-500'
                      : 'bg-gray-600'
                  }`}
                  title={`Scene ${index + 1}`}
                />
              ))}
            </div>

            {/* Play Controls */}
            <div className="flex items-center gap-4">
              <button
                onClick={handleRestart}
                className="w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center transition-all"
              >
                <RotateCcw className="w-6 h-6 text-white" />
              </button>

              <button
                onClick={handlePlayPause}
                className="w-16 h-16 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-500 hover:to-blue-500 rounded-full flex items-center justify-center transition-all hover:scale-110"
              >
                {isPlaying ? (
                  <Pause className="w-8 h-8 text-white" />
                ) : (
                  <Play className="w-8 h-8 text-white ml-1" />
                )}
              </button>
            </div>

            {/* Scene Info */}
            <div className="text-right text-white">
              <div className="font-bold">Scene {currentScene + 1} of {scenes.length}</div>
              <div className="text-sm text-gray-400">
                {isPlaying ? 'Playing...' : 'Paused'}
              </div>
            </div>
          </div>
        </div>

        {/* Scene List */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          {scenes.map((sceneItem, index) => (
            <button
              key={index}
              onClick={() => setCurrentScene(index)}
              className={`p-4 rounded-lg text-left transition-all ${
                index === currentScene
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <div className="flex items-center gap-3">
                <span className="text-2xl">{sceneItem.character}</span>
                <div>
                  <div className="font-semibold">{sceneItem.title}</div>
                  <div className="text-xs opacity-75">{sceneItem.location}</div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </Section>
  );
};

export default DeveloperJourney3D;
