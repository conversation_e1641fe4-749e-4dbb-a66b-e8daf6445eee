import React, { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Card, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { Building2, Calendar, ExternalLink, MapPin, Clock, ChevronDown, Briefcase, Award, TrendingUp, Users } from 'lucide-react';
import Link from 'next/link';

interface Experience {
  id: number;
  company: string;
  position: string;
  period: string;
  duration: string;
  current: boolean;
  description: string;
  achievements: string[];
  technologies: string[];
  website: string;
  location: string;
  companyType: string;
}

const experiences: Experience[] = [
  {
    id: 1,
    company: "Serviots Technology",
    position: "Full Stack Developer",
    period: "October 2024 - Present",
    duration: "3+ months",
    current: true,
    location: "Onsite (Ahmedabad)",
    companyType: "Software Development Company",
    description: "Developing scalable web applications and enterprise solutions using modern technologies. Leading frontend development and collaborating with cross-functional teams.",
    achievements: [
      "Developed and maintained scalable web applications using the MERN stack",
      "Collaborated with designers and backend teams to implement seamless user experiences",
      "Optimized application performance for speed and scalability by 40%",
      "Debugged and resolved technical issues across frontend and backend systems",
      "Led development of 3 major client projects with successful delivery"
    ],
    technologies: ["React.js", "Next.js", "Node.js", "Express", "MongoDB", "Prisma", "PostgreSQL", "AWS", "Docker", "Redis", "TypeScript"],
    website: "https://serviots.com"
  },
  {
    id: 2,
    company: "Eyefounder",
    position: "Full Stack Developer",
    period: "March 2024 - August 2024",
    duration: "6 months",
    current: false,
    location: "Onsite (Budaun)",
    companyType: "Digital Marketing & Web Development",
    description: "Collaborated with cross-functional teams to design, develop, and deploy innovative web applications for diverse business clients.",
    achievements: [
      "Collaborated with cross-functional teams to design, develop, and deploy web applications",
      "Utilized comprehensive stack of technologies including frontend and backend frameworks",
      "Managed databases and server infrastructure efficiently",
      "Delivered 5+ client projects on time and within budget",
      "Reduced development time by 30% through process optimization"
    ],
    technologies: ["React.js", "Next.js", "Node.js", "Express", "MongoDB", "TypeScript", "WordPress", "CMS"],
    website: "https://eyefounder.com"
  },
  {
    id: 3,
    company: "Zafron Technology",
    position: "Full Stack MERN Developer",
    period: "July 2023 - February 2024",
    duration: "8 months",
    current: false,
    location: "Remote",
    companyType: "Software Development Company",
    description: "Developed full-stack web applications with focus on modern technologies and user experience. Built complete food delivery platform from scratch.",
    achievements: [
      "Developed a full-stack food web application using Node.js, Express, and MongoDB",
      "Designed and implemented RESTful API for user authentication and recipe management",
      "Built responsive frontend interfaces with React and modern CSS",
      "Integrated third-party APIs and payment gateways",
      "Achieved 99% uptime with robust error handling and monitoring"
    ],
    technologies: ["React", "JavaScript", "SASS", "Redux", "Node.js", "MongoDB", "Express"],
    website: "https://www.linkedin.com/company/zafron-technologies-and-solutions/"
  }
];

// Helper to calculate months between two dates
type MonthDiffOptions = { endDate?: Date };
function getMonthDiff(start: string, end: string | null, options: MonthDiffOptions = {}) {
  const startDate = new Date(start);
  let endDate: Date;
  if (end && end.toLowerCase() !== 'present') {
    endDate = new Date(end);
  } else if (options.endDate) {
    endDate = options.endDate;
  } else {
    endDate = new Date();
  }
  let months = (endDate.getFullYear() - startDate.getFullYear()) * 12;
  months += endDate.getMonth() - startDate.getMonth();
  if (endDate.getDate() < startDate.getDate()) months--;
  return months <= 0 ? 'Less than 1 month' : months === 1 ? '1 month' : `${months} months`;
}

// Parse period string like "October 2024 - Present" to get start and end
function parsePeriod(period: string) {
  const [start, end] = period.split(' - ');
  return { start, end: end || null };
}

// Update durations dynamically
const now = new Date('2025-06-07');
const updatedExperiences = experiences.map((exp) => {
  const { start, end } = parsePeriod(exp.period);
  return {
    ...exp,
    duration: getMonthDiff(start + ' 1', end, { endDate: now }),
  };
});

const getCompanyInitials = (name: string) => {
  return name
    .split(' ')
    .map((word) => word[0])
    .join('')
    .toUpperCase();
};

const TimelineItem: React.FC<{ experience: Experience; index: number; isLast: boolean }> = ({ experience, index, isLast }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  return (
    <div className="relative">
      {/* Timeline Item */}
      <div className="flex items-start">
        {/* Timeline Line and Dot */}
        <div className="flex flex-col items-center mr-8 relative">
          {/* Animated Timeline Dot with Company Initials */}
          <motion.div
            ref={ref}
            initial={{ scale: 0, opacity: 0 }}
            animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
            transition={{ delay: index * 0.15, duration: 0.5 }}
            className="relative z-10"
          >
            <div className={`w-14 h-14 rounded-full flex items-center justify-center border-4 shadow-xl transition-all duration-300
              ${experience.current
                ? 'bg-gradient-to-br from-green-400 via-green-600 to-green-400 border-green-300 shadow-green-400/40'
                : 'bg-gradient-to-br from-blue-400 via-blue-600 to-blue-400 border-blue-300 shadow-blue-400/40'}
            `}>
              <span className="text-white text-2xl font-extrabold drop-shadow-lg select-none">
                {getCompanyInitials(experience.company)}
              </span>
              {/* Animated Glow */}
              <span className={`absolute inset-0 rounded-full pointer-events-none animate-pulse
                ${experience.current ? 'bg-green-400/30' : 'bg-blue-400/30'}
              `} />
            </div>
            {/* Year Badge */}
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
              transition={{ delay: index * 0.15 + 0.2 }}
              className="absolute left-16 top-1/2 -translate-y-1/2 bg-gradient-to-r from-primary-600 to-secondary-600 text-white text-xs px-3 py-1 rounded-full font-bold border-2 border-white/30 shadow-lg shadow-black/10"
            >
              {experience.period.split(' - ')[0].split(' ')[1]}
            </motion.div>
          </motion.div>

          {/* Connecting Line with Gradient Pulse */}
          {!isLast && (
            <motion.div
              initial={{ height: 0 }}
              animate={isInView ? { height: '100%' } : { height: 0 }}
              transition={{ delay: index * 0.15 + 0.3, duration: 0.8 }}
              className="w-2 relative flex justify-center"
              style={{ minHeight: 120 }}
            >
              <div className="absolute left-1/2 -translate-x-1/2 top-0 bottom-0 w-1.5 bg-gradient-to-b from-primary-400 via-secondary-400 to-blue-400 animate-gradient-pulse rounded-full" />
            </motion.div>
          )}
        </div>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, x: 30 }}
          animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 30 }}
          transition={{ delay: index * 0.15 + 0.1, duration: 0.6 }}
          className="flex-1 pb-16"
        >
          <div className="bg-white/10 backdrop-blur-lg border-2 border-primary-400/30 rounded-2xl hover:border-primary-400/60 transition-all duration-300 hover:shadow-2xl shadow-primary-400/10 relative overflow-hidden group">
            {/* Glassmorphism Glow */}
            <div className="absolute -inset-1 bg-gradient-to-br from-primary-400/10 via-secondary-400/10 to-blue-400/10 blur-2xl opacity-60 group-hover:opacity-80 transition-all duration-300 pointer-events-none" />
            {/* Header */}
            <div className="p-8 border-b border-white/10 relative z-10">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  {/* Current Badge */}
                  {experience.current && (
                    <div className="inline-flex items-center gap-2 bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm font-medium mb-3 border border-green-500/30">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                      Currently Working
                    </div>
                  )}

                  <h3 className="text-2xl font-extrabold text-white mb-2 hover:text-primary-300 transition-colors">
                    {experience.company}
                  </h3>
                  <p className="text-gray-400 text-sm mb-3">{experience.companyType}</p>

                  <h4 className="text-xl font-semibold text-primary-400 mb-4">
                    {experience.position}
                  </h4>

                  {/* Timeline Info */}
                  <div className="flex flex-wrap gap-4 text-sm">
                    <div className="flex items-center gap-2 text-gray-300">
                      <Calendar size={14} className="text-primary-400" />
                      <span>{experience.period}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-300">
                      <Clock size={14} className="text-secondary-400" />
                      <span>{experience.duration}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-300">
                      <MapPin size={14} className="text-primary-400" />
                      <span>{experience.location}</span>
                    </div>
                  </div>
                </div>

                <Link
                  href={experience.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-lg bg-white/10 hover:bg-white/20 text-gray-400 hover:text-white transition-all duration-300 hover:scale-110"
                >
                  <ExternalLink size={16} />
                </Link>
              </div>
            </div>

            {/* Content */}
            <div className="p-8 relative z-10">
              {/* Description */}
              <p className="text-gray-200 leading-relaxed mb-6 text-base">
                {experience.description}
              </p>

              {/* Achievements */}
              <div className="mb-6">
                <h5 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
                  <Award size={16} className="text-yellow-400" />
                  Key Achievements
                </h5>
                <div className="space-y-2">
                  {experience.achievements.slice(0, isExpanded ? experience.achievements.length : 3).map((achievement, idx) => (
                    <div key={idx} className="flex items-start gap-3 text-sm text-gray-200">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary-400 mt-2 flex-shrink-0" />
                      <span>{achievement}</span>
                    </div>
                  ))}
                </div>

                {experience.achievements.length > 3 && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="mt-3 text-sm text-primary-400 hover:text-primary-300 transition-colors flex items-center gap-1"
                  >
                    {isExpanded ? 'Show Less' : `Show ${experience.achievements.length - 3} More`}
                    <ChevronDown size={14} className={`transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} />
                  </button>
                )}
              </div>

              {/* Technologies */}
              <div>
                <h5 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                  <Briefcase size={16} className="text-primary-400" />
                  Technologies
                </h5>
                <div className="flex flex-wrap gap-2">
                  {experience.technologies.map((tech, idx) => (
                    <Badge
                      key={idx}
                      variant={idx % 2 === 0 ? 'default' : 'secondary'}
                      className="text-xs hover:scale-105 transition-transform duration-200"
                    >
                      {tech}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

const Experience: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <Section id="experience" className="bg-background relative overflow-hidden py-24">
      {/* Enhanced background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background to-background"></div>
        <div className="absolute top-1/3 left-1/4 w-96 h-96 bg-primary-500/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-secondary-500/8 rounded-full blur-3xl"></div>

        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }} />
        </div>
      </div>

      <SectionTitle
        title="Work Experience"
        subtitle="Professional journey in full-stack development with proven results across multiple companies"
        centered
      />

      {/* Professional Timeline */}
      <div className="relative max-w-5xl mx-auto">
        {/* Timeline Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full border border-primary-500/30 backdrop-blur-sm shadow-lg shadow-primary-500/10">
            <Briefcase size={16} className="text-primary-400" />
            <span className="text-sm text-gray-300 font-medium">
              Career Timeline
            </span>
            <TrendingUp size={16} className="text-secondary-400" />
          </div>
        </motion.div>

        {/* Timeline Container */}
        <div className="relative">
          {/* Main Timeline Line - now a vibrant gradient and animated */}
          <div className="absolute left-6 top-0 bottom-0 w-1 bg-gradient-to-b from-primary-400 via-secondary-400 to-pink-400 animate-pulse rounded-full shadow-lg shadow-primary-500/20"></div>

          {/* Timeline Items */}
          <div className="space-y-12">
            {updatedExperiences.map((exp, index) => (
              <motion.div
                key={exp.id}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.7, type: 'spring' }}
                className="relative flex items-start group"
              >
                {/* Creative Timeline Dot with animated border and initials, floating effect, and animated glow */}
                <div className="flex flex-col items-center mr-8 z-10">
                  <motion.div
                    className={`w-16 h-16 rounded-full border-4 border-white bg-gradient-to-br from-primary-500 via-secondary-500 to-pink-400 flex items-center justify-center text-white text-2xl font-extrabold shadow-xl shadow-primary-500/30 group-hover:scale-110 transition-transform duration-300 relative animate-spin-slow animate-float`}
                    title={exp.company}
                    animate={{ y: [0, -10, 0] }}
                    transition={{ duration: 3 + index * 0.2, repeat: Infinity, ease: 'easeInOut' }}
                  >
                    <span className="drop-shadow-lg select-none tracking-widest">
                      {getCompanyInitials(exp.company)}
                    </span>
                    {/* Animated Glow */}
                    <span className={`absolute inset-0 rounded-full pointer-events-none animate-pulse ${exp.current ? 'bg-green-400/30' : 'bg-blue-400/30'}`} />
                  </motion.div>
                  {/* Animated connecting line with floating dots */}
                  {index !== updatedExperiences.length - 1 && (
                    <div className="w-1 h-24 bg-gradient-to-b from-primary-400 via-secondary-400 to-pink-400 opacity-70 group-hover:opacity-100 transition-all duration-300 animate-gradient-pulse relative flex flex-col items-center justify-center">
                      {/* Floating dots on the line */}
                      <motion.div
                        className="w-3 h-3 rounded-full bg-primary-400/70 absolute left-1/2 -translate-x-1/2 top-4 shadow-lg animate-pulse"
                        animate={{ y: [0, 12, 0] }}
                        transition={{ duration: 2, repeat: Infinity, delay: index * 0.2, ease: 'easeInOut' }}
                      />
                      <motion.div
                        className="w-2 h-2 rounded-full bg-secondary-400/70 absolute left-1/2 -translate-x-1/2 bottom-4 shadow-md animate-pulse"
                        animate={{ y: [0, -12, 0] }}
                        transition={{ duration: 2, repeat: Infinity, delay: index * 0.3, ease: 'easeInOut' }}
                      />
                    </div>
                  )}
                </div>

                {/* Experience Card - modern glassmorphism, hover pop, colored border */}
                <div className="flex-1">
                  <div className="bg-white/10 backdrop-blur-xl border-2 border-primary-500/20 group-hover:border-secondary-500/40 rounded-2xl shadow-xl transition-all duration-300 hover:scale-[1.025] hover:shadow-2xl p-8 relative overflow-hidden">
                    {/* Company & Position */}
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
                      <div>
                        <h3 className="text-2xl font-bold text-white mb-1 flex items-center gap-2">
                          {exp.company}
                          {exp.current && <span className="ml-2 px-3 py-1 rounded-full bg-green-500/20 text-green-400 text-xs font-semibold border border-green-400/30 animate-pulse">Current</span>}
                        </h3>
                        <p className="text-primary-400 text-lg font-semibold mb-1">{exp.position}</p>
                        <p className="text-gray-400 text-sm">{exp.companyType}</p>
                      </div>
                      <Link
                        href={exp.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 rounded-lg bg-white/10 hover:bg-primary-500/20 text-primary-400 hover:text-white transition-all duration-300 hover:scale-110 border border-primary-500/20"
                      >
                        <ExternalLink size={18} />
                      </Link>
                    </div>

                    {/* Timeline Info */}
                    <div className="flex flex-wrap gap-6 text-sm mb-4">
                      <div className="flex items-center gap-2 text-gray-300">
                        <Calendar size={14} className="text-primary-400" />
                        <span>{exp.period}</span>
                      </div>
                      {/* <div className="flex items-center gap-2 text-gray-300">
                        <Clock size={14} className="text-secondary-400" />
                        <span>{exp.duration}</span>
                      </div> */}
                      <div className="flex items-center gap-2 text-gray-300">
                        <MapPin size={14} className="text-primary-400" />
                        <span>{exp.location}</span>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-gray-200 leading-relaxed mb-6 text-base italic">{exp.description}</p>

                    {/* Achievements */}
                    <div className="mb-6">
                      <h5 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
                        <Award size={16} className="text-yellow-400" />
                        Key Achievements
                      </h5>
                      <ul className="space-y-2 list-disc list-inside pl-2">
                        {exp.achievements.map((achievement, idx) => (
                          <li key={idx} className="text-sm text-gray-300">{achievement}</li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div>
                      <h5 className="text-sm font-semibold text-foreground mb-3 flex items-center gap-2">
                        <Briefcase size={16} className="text-primary-400" />
                        Technologies
                      </h5>
                      <div className="flex flex-wrap gap-2">
                        {exp.technologies.map((tech, idx) => (
                          <Badge
                            key={idx}
                            variant={idx % 2 === 0 ? 'default' : 'secondary'}
                            className="text-xs hover:scale-105 transition-transform duration-200"
                          >
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ delay: 0.6 }}
        className="mt-20 text-center"
      >
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full border border-primary-500/30 backdrop-blur-sm">
          <Users size={16} className="text-primary-400" />
          <span className="text-sm text-gray-300">
            Ready to bring this experience to your team
          </span>
          <TrendingUp size={16} className="text-secondary-400" />
        </div>
      </motion.div>
    </Section>
  );
};

export default Experience;

/* Add this to your global CSS (e.g., globals.css) for the creative timeline: */
/*
@keyframes spin-slow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}
.animate-float {
  animation: float 4s ease-in-out infinite;
}
*/
