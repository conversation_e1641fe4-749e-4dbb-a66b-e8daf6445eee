@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  /* Light mode specific styles */
  :root {
    --meteor-color: rgba(0, 0, 0, 0.8);
    --particle-color: rgba(99, 102, 241, 0.3);
  }

  .dark {
    --meteor-color: rgba(255, 255, 255, 0.8);
    --particle-color: rgba(99, 102, 241, 0.3);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  
  body {
    @apply bg-background text-foreground antialiased selection:bg-primary-500/30;
    overflow-x: hidden;
  }

  ::-webkit-scrollbar {
    width: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-primary-600/50 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-500;
  }
}

@layer components {
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary-400 via-secondary-400 to-primary-600;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .meteor-effect {
    position: absolute;
    width: 1px;
    height: 50px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.8));
    border-radius: 0 0 2px 0;
    animation-duration: 3000ms;
    animation-delay: var(--delay);
  }

  .glass-card {
    @apply backdrop-blur-md bg-foreground/5 border border-foreground/10 hover:border-foreground/20 transition-all duration-500;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-card:hover {
    box-shadow: 0 12px 40px rgba(99, 102, 241, 0.15);
  }

  .gradient-border {
    --border-width: 2px;
    position: relative;
    border-radius: inherit;
  }

  .gradient-border::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: var(--border-width);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    background: linear-gradient(135deg, #6366f1, #8b5cf6, #6366f1);
    -webkit-mask-composite: xor;
    pointer-events: none;
  }

  .enhanced-card {
    @apply relative overflow-hidden rounded-2xl bg-gradient-to-br from-foreground/10 to-foreground/5 backdrop-blur-xl border border-foreground/20;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .enhanced-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  }

  .glow-effect {
    position: relative;
  }

  .glow-effect::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(139, 92, 246, 0.3));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    filter: blur(20px);
    z-index: -1;
  }

  .glow-effect:hover::after {
    opacity: 1;
  }

  /* Line clamp utility */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.custom-cursor {
  position: fixed;
  pointer-events: none;
  mix-blend-mode: difference;
  z-index: 9999;
  display: none;
}

@media (pointer: fine) {
  .custom-cursor {
    display: block;
  }
}