'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Card, CardContent } from './ui/Card';
import { Button } from './ui/Button';
import { Mail, MapPin, Phone, Send, MessageCircle, Calendar, Clock, Globe, Github, Linkedin, Instagram, ExternalLink, Sparkles } from 'lucide-react';
import toast from 'react-hot-toast';

const Contact: React.FC = () => {
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const res = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formState),
      });

      const data = await res.json();

      if (res.ok) {
        setIsSubmitted(true);
        setFormState({
          name: '',
          email: '',
          subject: '',
          message: '',
        });
        toast.success('Message sent successfully!');
      } else {
        toast.error(`Error: ${data.message}`);
      }
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Section id="contact" className="bg-background relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background to-background"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/8 rounded-full blur-3xl"></div>
        
        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }} />
        </div>
      </div>

      <SectionTitle
        title="Get In Touch"
        subtitle="Let's discuss your project and create something amazing together"
        centered
      />

      {/* Simple Contact Info */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.6 }}
        className="max-w-4xl mx-auto mb-16"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-8 bg-foreground/5 backdrop-blur-sm border border-foreground/10 rounded-xl hover:border-foreground/20 transition-all duration-300">
            <div className="mb-4">
              <Mail className="w-8 h-8 text-primary-400 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-3">Email</h3>
            <a href="mailto:<EMAIL>" className="text-foreground/70 hover:text-primary-400 transition-colors text-sm">
              <EMAIL>
            </a>
          </div>
          <div className="text-center p-8 bg-foreground/5 backdrop-blur-sm border border-foreground/10 rounded-xl hover:border-foreground/20 transition-all duration-300">
            <div className="mb-4">
              <Phone className="w-8 h-8 text-secondary-400 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-3">Phone</h3>
            <a href="tel:+917817837369" className="text-foreground/70 hover:text-secondary-400 transition-colors text-sm">
              +91 7817837369
            </a>
          </div>
          <div className="text-center p-8 bg-foreground/5 backdrop-blur-sm border border-foreground/10 rounded-xl hover:border-foreground/20 transition-all duration-300">
            <div className="mb-4">
              <MapPin className="w-8 h-8 text-primary-400 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-3">Location</h3>
            <p className="text-foreground/70 text-sm">Ahmedabad, Gujarat</p>
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-foreground/5 backdrop-blur-sm border border-foreground/10 hover:border-foreground/20 transition-all duration-300">
            <CardContent className="p-8 pt-12">
              <h3 className="text-2xl font-bold text-foreground mb-6">Let's Work Together</h3>

              <p className="text-foreground/70 leading-relaxed mb-8">
                Ready to turn your ideas into reality? I specialize in building modern, scalable solutions for businesses and individuals. Share your vision or challenge—let’s create something exceptional together!
              </p>

              {/* Availability Status */}
              <div className="flex items-center gap-3 p-4 bg-green-500/10 rounded-lg border border-green-500/20 mb-8">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 font-medium">Available for new projects</span>
              </div>

              {/* Social Links */}
              <div>
                <h4 className="text-lg font-semibold text-foreground mb-4">Connect With Me</h4>
                <div className="flex gap-4">
                  <a
                    href="https://www.linkedin.com/in/harish-rawal-b4024b211/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center w-12 h-12 bg-blue-600 hover:bg-blue-500 rounded-lg transition-colors duration-300"
                  >
                    <Linkedin className="w-6 h-6 text-white" />
                  </a>
                  <a
                    href="https://github.com/Harishrawal2"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center w-12 h-12 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors duration-300"
                  >
                    <Github className="w-6 h-6 text-white" />
                  </a>
                  <a
                    href="https://www.instagram.com/harish_rawal8126/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-400 hover:to-purple-500 rounded-lg transition-all duration-300"
                  >
                    <Instagram className="w-6 h-6 text-white" />
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-foreground/5 backdrop-blur-sm border border-foreground/10 hover:border-foreground/20 transition-all duration-300">
            <CardContent className="p-8 pt-12">
              <h3 className="text-2xl font-bold text-foreground mb-6">Send Me a Message</h3>

              {isSubmitted ? (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-8"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-500/20 mb-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-500">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                  </div>
                  <h4 className="text-xl font-semibold mb-2">Thanks for Getting in Touch!</h4>
                  <p className="text-foreground/60">
                    I appreciate you reaching out and reviewing my portfolio. Whether you're a business or an individual,
                    I look forward to learning more about how I can help. I'll be in touch very soon!
                  </p>
                  <Button
                    className="mt-6"
                    onClick={() => setIsSubmitted(false)}
                  >
                    Send Another Message
                  </Button>
                </motion.div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-foreground/70 mb-2">
                        Full Name *
                      </label>
                      <input
                        id="name"
                        name="name"
                        type="text"
                        required
                        value={formState.name}
                        onChange={handleChange}
                        placeholder="Enter your full name"
                        className="w-full px-4 py-3 bg-foreground/5 border border-foreground/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 text-foreground placeholder-foreground/40 transition-all duration-300"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-foreground/70 mb-2">
                        Email Address *
                      </label>
                      <input
                        id="email"
                        name="email"
                        type="email"
                        required
                        value={formState.email}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-3 bg-foreground/5 border border-foreground/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 text-foreground placeholder-foreground/40 transition-all duration-300"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-foreground/70 mb-2">
                      Subject *
                    </label>
                    <input
                      id="subject"
                      name="subject"
                      type="text"
                      required
                      value={formState.subject}
                      onChange={handleChange}
                      placeholder="What's your project about?"
                      className="w-full px-4 py-3 bg-foreground/5 border border-foreground/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 text-foreground placeholder-foreground/40 transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-foreground/70 mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      required
                      value={formState.message}
                      onChange={handleChange}
                      placeholder="Tell me about your project requirements..."
                      className="w-full px-4 py-3 bg-foreground/5 border border-foreground/10 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 text-foreground placeholder-foreground/40 resize-none transition-all duration-300"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full py-3 px-6 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    variant="primary"
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center">
                        <Send size={18} className="mr-2" />
                        Send Message
                      </span>
                    )}
                  </Button>

                  <div className="text-center pt-4">
                    <p className="text-sm text-foreground/60">
                      I'll get back to you within 24 hours
                    </p>
                  </div>
                </form>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </Section>
  );
};

export default Contact;
