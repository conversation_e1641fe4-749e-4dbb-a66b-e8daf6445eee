import React from 'react';
import { motion } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Card, CardContent } from './ui/Card';
import { Badge } from './ui/Badge';
import { Code, BookOpen, GraduationCap, Briefcase, Award, Target, Users, Zap } from 'lucide-react';

const About: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12,
      },
    },
  };

  const stats = [
    { number: "2+", label: "Years Experience", icon: Briefcase },
    { number: "15+", label: "Projects Completed", icon: Award },
    { number: "99.9%", label: "Client Satisfaction", icon: Target },
    { number: "24/7", label: "Support Available", icon: Zap },
  ];

  return (
    <Section id="about" className="bg-background relative overflow-hidden">
      {/* Enhanced background with multiple gradients */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,rgba(99,102,241,0.15),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_left,rgba(139,92,246,0.15),transparent_50%)]"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/5 rounded-full blur-3xl"></div>
      </div>

      <SectionTitle
        title="About Me"
        subtitle="Passionate software engineer crafting digital solutions that make a difference"
        mxAuto='none'
      />

      {/* Stats section */}
      <motion.div
        className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className="text-center group"
          >
            <div className="relative mx-auto w-16 h-16 mb-4">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-2xl group-hover:from-primary-500/30 group-hover:to-secondary-500/30 transition-all duration-300"></div>
              <div className="relative flex items-center justify-center w-full h-full">
                <stat.icon className="w-8 h-8 text-primary-400 group-hover:text-primary-300 transition-colors duration-300" />
              </div>
            </div>
            <div className="text-3xl font-bold text-foreground mb-1">{stat.number}</div>
            <div className="text-sm text-foreground/60">{stat.label}</div>
          </motion.div>
        ))}
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <motion.div
          className="lg:col-span-2"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div variants={itemVariants}>
            <Card className="h-full group bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 hover:border-white/20 transition-all duration-500 hover:shadow-2xl hover:shadow-primary-500/10">
              <CardContent className="p-8 lg:p-10">
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-14 h-14 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 rounded-xl flex items-center justify-center group-hover:from-primary-500/30 group-hover:to-secondary-500/30 transition-all duration-300 group-hover:scale-110">
                    <Users className="w-7 h-7 text-primary-400 group-hover:text-primary-300 transition-colors duration-300" />
                  </div>
                  <h3 className="text-3xl lg:text-4xl font-bold text-gradient group-hover:text-primary-300 transition-colors duration-300">My Journey</h3>
                </div>

                <div className="space-y-8">
                  <div className="relative pl-8 border-l-2 border-primary-500/30">
                    <div className="absolute -left-[9px] top-1 w-4 h-4 bg-primary-500 rounded-full shadow-lg shadow-primary-500/50"></div>
                    <p className="text-foreground/70 leading-relaxed text-base">
                      With over <span className="text-primary-400 font-semibold">2+ years</span> of experience in software development,
                      I've crafted solutions ranging from dynamic web applications to complex enterprise systems,
                      always focusing on <span className="text-secondary-400 font-semibold">user experience</span> and
                      <span className="text-primary-400 font-semibold"> scalable architecture</span>.
                    </p>
                  </div>

                  <div className="relative pl-8 border-l-2 border-secondary-500/30">
                    <div className="absolute -left-[9px] top-1 w-4 h-4 bg-secondary-500 rounded-full shadow-lg shadow-secondary-500/50"></div>
                    <p className="text-foreground/70 leading-relaxed text-base">
                      My expertise spans the full development lifecycle - from conceptualization and design to deployment and maintenance.
                      I'm passionate about <span className="text-primary-400 font-semibold">clean code</span>,
                      <span className="text-secondary-400 font-semibold"> performance optimization</span>, and creating
                      digital experiences that truly matter.
                    </p>
                  </div>

                  <div className="relative pl-8 border-l-2 border-primary-500/30">
                    <div className="absolute -left-[9px] top-1 w-4 h-4 bg-primary-500 rounded-full shadow-lg shadow-primary-500/50"></div>
                    <p className="text-foreground/70 leading-relaxed text-base">
                      Beyond coding, I'm an active contributor to the tech community - sharing knowledge through
                      <span className="text-secondary-400 font-semibold"> Open-source Projects</span>, mentoring aspiring developers,
                      and staying at the forefront of emerging technologies.
                    </p>
                  </div>
                </div>

                <div className="mt-10 pt-8 border-t border-white/10">
                  <h4 className="text-xl font-semibold mb-6 text-foreground flex items-center gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-primary-400 to-secondary-400 rounded-full"></div>
                    Core Technologies
                  </h4>
                  <div className="flex flex-wrap gap-3">
                    {['JavaScript', 'TypeScript', 'React', 'Next.js', 'Node.js', 'Python', 'MongoDB', 'PostgreSQL', 'AWS', 'Docker'].map((tech, index) => (
                      <Badge
                        key={index}
                        variant={index % 2 === 0 ? 'default' : 'secondary'}
                        className="hover:scale-105 transition-transform duration-200 text-sm py-2 px-4"
                      >
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        <motion.div
          className="lg:col-span-1"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="grid grid-cols-1 gap-6">
            <motion.div variants={itemVariants}>
              <Card className="group bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 hover:border-primary-500/30 hover:scale-[1.02] transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/20">
                <CardContent className="p-5 pt-5">
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 p-2.5 rounded-lg group-hover:from-primary-500/30 group-hover:to-primary-600/30 transition-all duration-300 flex-shrink-0">
                      <Code size={18} className="text-primary-400 group-hover:text-primary-300 transition-colors duration-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base font-semibold text-foreground group-hover:text-primary-300 transition-colors duration-300 mb-1">Full-Stack Development</h3>
                      <p className="text-foreground/60 text-xs leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">Crafting end-to-end solutions with modern frameworks and best practices</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="group bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 hover:border-secondary-500/30 hover:scale-[1.02] transition-all duration-300 hover:shadow-xl hover:shadow-secondary-500/20">
                <CardContent className="p-5 pt-5">
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-br from-secondary-500/20 to-secondary-600/20 p-2.5 rounded-lg group-hover:from-secondary-500/30 group-hover:to-secondary-600/30 transition-all duration-300 flex-shrink-0">
                      <BookOpen size={18} className="text-secondary-400 group-hover:text-secondary-300 transition-colors duration-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base font-semibold text-foreground group-hover:text-secondary-300 transition-colors duration-300 mb-1">Continuous Learning</h3>
                      <p className="text-foreground/60 text-xs leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">Staying ahead with emerging technologies and industry trends</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="group bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 hover:border-primary-500/30 hover:scale-[1.02] transition-all duration-300 hover:shadow-xl hover:shadow-primary-500/20">
                <CardContent className="p-5 pt-5">
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-br from-primary-500/20 to-primary-600/20 p-2.5 rounded-lg group-hover:from-primary-500/30 group-hover:to-primary-600/30 transition-all duration-300 flex-shrink-0">
                      <GraduationCap size={18} className="text-primary-400 group-hover:text-primary-300 transition-colors duration-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base font-semibold text-foreground group-hover:text-primary-300 transition-colors duration-300 mb-1">Education</h3>
                      <p className="text-foreground/60 text-xs leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">B.Tech in Computer Science & Engineering with honors</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="group bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 hover:border-secondary-500/30 hover:scale-[1.02] transition-all duration-300 hover:shadow-xl hover:shadow-secondary-500/20">
                <CardContent className="p-5 pt-5">
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-br from-secondary-500/20 to-secondary-600/20 p-2.5 rounded-lg group-hover:from-secondary-500/30 group-hover:to-secondary-600/30 transition-all duration-300 flex-shrink-0">
                      <Briefcase size={18} className="text-secondary-400 group-hover:text-secondary-300 transition-colors duration-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base font-semibold text-foreground group-hover:text-secondary-300 transition-colors duration-300 mb-1">Professional Experience</h3>
                      <p className="text-foreground/60 text-xs leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">2+ years building scalable applications and leading projects</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="group bg-gradient-to-br from-white/5 to-white/[0.02] backdrop-blur-xl border border-white/10 hover:border-secondary-500/30 hover:scale-[1.02] transition-all duration-300 hover:shadow-xl hover:shadow-secondary-500/20">
                <CardContent className="p-5 pt-5">
                  <div className="flex items-center gap-3">
                    <div className="bg-gradient-to-br from-secondary-500/20 to-secondary-600/20 p-2.5 rounded-lg group-hover:from-secondary-500/30 group-hover:to-secondary-600/30 transition-all duration-300 flex-shrink-0">
                      <Briefcase size={18} className="text-secondary-400 group-hover:text-secondary-300 transition-colors duration-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-base font-semibold text-foreground group-hover:text-secondary-300 transition-colors duration-300 mb-1">Open Source Contribution</h3>
                      <p className="text-foreground/60 text-xs leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">            Actively contributing to React, Node, and developer tooling projects on GitHub
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </Section>
  );
};

export default About;