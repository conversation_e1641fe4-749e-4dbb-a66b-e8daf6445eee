import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Card, CardContent } from './ui/Card';
import { Quote, ChevronLeft, ChevronRight, Star } from 'lucide-react';
import Image from 'next/image';

interface Testimonial {
  id: number;
  name: string;
  position: string;
  company: string;
  image: string;
  quote: string;
  rating: number;
  project: string;
  duration: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    position: "CEO",
    company: "TechStart Inc",
    image: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "Working with <PERSON><PERSON> was an absolute pleasure. Their attention to detail and technical expertise helped us launch our product ahead of schedule. The React application they built exceeded our expectations in both performance and user experience.",
    rating: 5,
    project: "E-commerce Platform",
    duration: "3 months"
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "CTO",
    company: "InnovateLabs",
    image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "The quality of work delivered was exceptional. Harish brought innovative solutions to our complex technical challenges and delivered a robust full-stack application using the MERN stack. Highly recommended!",
    rating: 5,
    project: "SaaS Dashboard",
    duration: "4 months"
  },
  {
    id: 3,
    name: "Emily Rodriguez",
    position: "Product Manager",
    company: "DigitalFlow",
    image: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "Their expertise in modern web technologies and ability to deliver user-friendly solutions made our project a huge success. The Next.js application they developed is fast, scalable, and beautifully designed.",
    rating: 5,
    project: "Corporate Website",
    duration: "2 months"
  },
  {
    id: 4,
    name: "David Kumar",
    position: "Founder",
    company: "StartupHub",
    image: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "Harish delivered an outstanding mobile-responsive web application. Their problem-solving skills and dedication to quality made them an invaluable part of our team. The project was completed on time and within budget.",
    rating: 5,
    project: "Startup Platform",
    duration: "5 months"
  },
  {
    id: 5,
    name: "Lisa Thompson",
    position: "Marketing Director",
    company: "CreativeAgency",
    image: "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    quote: "Working with Harish was a game-changer for our agency. They created a stunning portfolio website with amazing animations and perfect responsive design. Our client satisfaction has increased significantly.",
    rating: 5,
    project: "Portfolio Website",
    duration: "6 weeks"
  }
];

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const getVisibleTestimonials = () => {
    const prev = currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1;
    const next = currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1;

    return {
      prev: testimonials[prev],
      current: testimonials[currentIndex],
      next: testimonials[next]
    };
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-foreground/30'}
      />
    ));
  };

  return (
    <Section id="testimonials" className="bg-background relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-background to-background"></div>
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-500/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary-500/8 rounded-full blur-3xl"></div>

        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }} />
        </div>
      </div>

      <SectionTitle
        title="Client Testimonials"
        subtitle="What clients say about working with me - real feedback from real projects"
        centered
      />

      {/* Center-focused Slider Container */}
      <div className="relative max-w-7xl mx-auto">
        {/* Navigation Controls */}
        <div className="flex items-center justify-center gap-4 mb-12">
          <button
            onClick={prevTestimonial}
            className="p-3 bg-foreground/5 hover:bg-foreground/10 rounded-full border border-foreground/20 text-foreground/60 hover:text-foreground transition-all duration-300 hover:scale-110"
          >
            <ChevronLeft size={20} />
          </button>

          <div className="text-sm text-foreground/60 px-4">
            {currentIndex + 1} of {testimonials.length}
          </div>

          <button
            onClick={nextTestimonial}
            className="p-3 bg-foreground/5 hover:bg-foreground/10 rounded-full border border-foreground/20 text-foreground/60 hover:text-foreground transition-all duration-300 hover:scale-110"
          >
            <ChevronRight size={20} />
          </button>
        </div>

        {/* Responsive Layout: Three Cards on Desktop, Single Card on Mobile */}
        <div className="relative">
          {/* Desktop: Three Card Layout */}
          <div className="hidden md:flex items-center justify-center gap-4 h-[500px] overflow-hidden">
            {(() => {
              const visible = getVisibleTestimonials();

              return (
                <>
                  {/* Left Half Card */}
                  <motion.div
                    key={`left-${currentIndex}`}
                    initial={{ opacity: 0, x: -100, scale: 0.8 }}
                    animate={{ opacity: 0.6, x: 0, scale: 0.8 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="w-80 h-96 cursor-pointer"
                    onClick={prevTestimonial}
                  >
                    <Card className="h-full bg-card/30 backdrop-blur-sm border-border/30 overflow-hidden">
                      <CardContent className="p-6 h-full flex flex-col justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-1 mb-4">
                            {renderStars(visible.prev.rating)}
                          </div>
                          <blockquote className="text-sm text-foreground/70 leading-relaxed mb-4 line-clamp-4">
                            "{visible.prev.quote}"
                          </blockquote>
                        </div>
                        <div className="flex items-center gap-3">
                          <Image
                            src={visible.prev.image}
                            alt={visible.prev.name}
                            width={48}
                            height={48}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                          <div>
                            <h4 className="font-semibold text-foreground text-sm">{visible.prev.name}</h4>
                            <p className="text-xs text-foreground/60">{visible.prev.position}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Center Active Card */}
                  <motion.div
                    key={`center-${currentIndex}`}
                    initial={{ opacity: 0, y: 50, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.6, ease: "easeInOut" }}
                    className="w-96 h-full z-10"
                  >
                    <Card className="h-full bg-card/50 backdrop-blur-xl border-primary-500/50 shadow-2xl shadow-primary-500/10 overflow-hidden">
                      <CardContent className="p-8 h-full flex flex-col justify-between">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-6">
                            <Quote className="text-primary-400" size={32} />
                            <div className="flex items-center gap-1">
                              {renderStars(visible.current.rating)}
                            </div>
                          </div>
                          <blockquote className="text-lg text-foreground leading-relaxed mb-6 italic">
                            "{visible.current.quote}"
                          </blockquote>
                        </div>
                        <div className="space-y-4">
                          <div className="flex items-center gap-4">
                            <div className="relative">
                              <Image
                                src={visible.current.image}
                                alt={visible.current.name}
                                width={64}
                                height={64}
                                className="w-16 h-16 rounded-full object-cover border-2 border-primary-400/50"
                              />
                            </div>
                            <div>
                              <h4 className="text-xl font-bold text-foreground">{visible.current.name}</h4>
                              <p className="text-primary-400 font-medium">{visible.current.position}</p>
                              <p className="text-foreground/60 text-sm">{visible.current.company}</p>
                            </div>
                          </div>
                          <div className="flex justify-between text-sm text-foreground/70">
                            <span><strong>Project:</strong> {visible.current.project}</span>
                            <span><strong>Duration:</strong> {visible.current.duration}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Right Half Card */}
                  <motion.div
                    key={`right-${currentIndex}`}
                    initial={{ opacity: 0, x: 100, scale: 0.8 }}
                    animate={{ opacity: 0.6, x: 0, scale: 0.8 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="w-80 h-96 cursor-pointer"
                    onClick={nextTestimonial}
                  >
                    <Card className="h-full bg-card/30 backdrop-blur-sm border-border/30 overflow-hidden">
                      <CardContent className="p-6 h-full flex flex-col justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-1 mb-4">
                            {renderStars(visible.next.rating)}
                          </div>
                          <blockquote className="text-sm text-foreground/70 leading-relaxed mb-4 line-clamp-4">
                            "{visible.next.quote}"
                          </blockquote>
                        </div>
                        <div className="flex items-center gap-3">
                          <Image
                            src={visible.next.image}
                            alt={visible.next.name}
                            width={48}
                            height={48}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                          <div>
                            <h4 className="font-semibold text-foreground text-sm">{visible.next.name}</h4>
                            <p className="text-xs text-foreground/60">{visible.next.position}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </>
              );
            })()}
          </div>

          {/* Mobile: Single Card Layout */}
          <div className="md:hidden">
            <motion.div
              key={`mobile-${currentIndex}`}
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -300 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="w-full max-w-md mx-auto"
            >
              <Card className="bg-card/50 backdrop-blur-xl border-primary-500/50 shadow-2xl shadow-primary-500/10 overflow-hidden">
                <CardContent className="p-6">
                  {/* Quote Section */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <Quote className="text-primary-400" size={24} />
                      <div className="flex items-center gap-1">
                        {renderStars(testimonials[currentIndex].rating)}
                      </div>
                    </div>
                    <blockquote className="text-base text-foreground leading-relaxed italic">
                      "{testimonials[currentIndex].quote}"
                    </blockquote>
                  </div>

                  {/* Client Info */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Image
                        src={testimonials[currentIndex].image}
                        alt={testimonials[currentIndex].name}
                        width={56}
                        height={56}
                        className="w-14 h-14 rounded-full object-cover border-2 border-primary-400/50"
                      />
                      <div>
                        <h4 className="text-lg font-bold text-foreground">{testimonials[currentIndex].name}</h4>
                        <p className="text-primary-400 font-medium text-sm">{testimonials[currentIndex].position}</p>
                        <p className="text-foreground/60 text-sm">{testimonials[currentIndex].company}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-foreground/70">
                      <div><strong>Project:</strong> {testimonials[currentIndex].project}</div>
                      <div><strong>Duration:</strong> {testimonials[currentIndex].duration}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>

        {/* Dots Indicator */}
        <div className="flex items-center justify-center gap-3 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${index === currentIndex
                ? 'bg-primary-400 scale-125'
                : 'bg-foreground/30 hover:bg-foreground/50'
                }`}
            />
          ))}
        </div>
      </div>

      {/* Stats Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ delay: 0.4 }}
        className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto"
      >
        {[
          { label: 'Happy Clients', value: '12+' },
          { label: 'Projects Completed', value: '15+' },
          { label: 'Average Rating', value: '5.0' },
          { label: 'Client Retention', value: '99.9%' },
        ].map((stat, index) => (
          <div key={index} className="text-center p-4 bg-white/5 rounded-xl backdrop-blur-sm border border-white/10">
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-sm text-gray-400">{stat.label}</div>
          </div>
        ))}
      </motion.div>
    </Section>
  );
};

export default Testimonials;