import React, { useRef, useEffect, useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { Button } from './ui/Button';
import { Container } from './ui/Container';
import { ArrowDown, Download, Mail, Github, Linkedin } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';


const techLogos = [
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg', alt: 'React', color: '#61DAFB' },
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg', alt: 'Node.js', color: '#339933' },
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg', alt: 'TypeScript', color: '#3178C6' },
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg', alt: 'MongoDB', color: '#47A248' },
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg', alt: 'JavaScript', color: '#F7DF1E' },
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg', alt: 'Next.js', color: '#000000' },
  { src: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg', alt: 'Python', color: '#3776AB' },
];

// Floating particles background
const FloatingParticles = () => {
  const [particles, setParticles] = useState<Array<{ id: number, x: number, y: number, delay: number }>>([]);

  useEffect(() => {
    const newParticles = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5,
    }));
    setParticles(newParticles);
  }, []);

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute w-1 h-1 bg-primary-400/30 dark:bg-primary-400/30 rounded-full"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
          }}
          animate={{
            y: [0, -100, 0],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 8,
            delay: particle.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

// Beautiful Floating Tech Icons with Smooth Animations
const FloatingTechIcons = () => {
  return (
    <div className="absolute inset-0 flex items-center justify-center">
      {/* Main circular arrangement */}
      {techLogos.map((logo, index) => {
        const angle = (360 / techLogos.length) * index;
        const radius = 180;
        const x = radius * Math.cos((angle * Math.PI) / 180);
        const y = radius * Math.sin((angle * Math.PI) / 180);

        return (
          <motion.div
            key={logo.alt}
            className="absolute"
            initial={{ x, y, scale: 0, opacity: 0 }}
            animate={{
              x: [
                x,
                x + Math.sin((index * 45 * Math.PI) / 180) * 20,
                x,
              ],
              y: [
                y,
                y + Math.cos((index * 45 * Math.PI) / 180) * 20,
                y,
              ],
              scale: [0, 1],
              opacity: [0, 1],
            }}
            transition={{
              x: {
                duration: 4 + index * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
              },
              y: {
                duration: 4 + index * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
              },
              scale: {
                duration: 0.8,
                delay: index * 0.1,
                ease: "backOut",
              },
              opacity: {
                duration: 0.8,
                delay: index * 0.1,
              },
            }}
          >
            <motion.div
              className="w-20 h-20 rounded-full bg-gradient-to-br from-foreground/10 to-foreground/5 backdrop-blur-xl border border-foreground/20 p-4 group cursor-pointer"
              whileHover={{
                scale: 1.2,
                rotate: 10,
                boxShadow: `0 20px 40px ${logo.color || '#6366f1'}40`,
                borderColor: logo.color || '#6366f1',
              }}
              whileTap={{ scale: 0.95 }}
              style={{
                background: `linear-gradient(135deg, ${logo.color || '#6366f1'}15, transparent)`,
              }}
            >
              <Image
                src={logo.src}
                alt={logo.alt}
                width={80}
                height={80}
                className="w-full h-full object-contain filter group-hover:brightness-110 transition-all duration-300"
              />

              {/* Glow effect */}
              <div
                className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                style={{
                  boxShadow: `inset 0 0 20px ${logo.color || '#6366f1'}30`,
                }}
              />
            </motion.div>
          </motion.div>
        );
      })}

      {/* Beautiful expanding rings */}
      {[1, 2, 3, 4].map((ring) => (
        <motion.div
          key={`ring-${ring}`}
          className="absolute rounded-full border border-primary-400/30"
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 2, 0],
            opacity: [0, 0.8, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            delay: ring * 1.5,
            ease: "easeOut",
          }}
          style={{
            width: `${40 + ring * 30}px`,
            height: `${40 + ring * 30}px`,
          }}
        />
      ))}
    </div>
  );
};



const Hero: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();
  useEffect(() => {
    controls.start("visible");
  }, [controls]);

  const handleScrollDown = () => {
    document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12,
      },
    },
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center pt-20 pb-8 overflow-hidden bg-background">
      {/* Enhanced background with multiple layers */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-background dark:from-gray-900 dark:via-gray-950 dark:to-gray-900"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(99,102,241,0.15),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_bottom_right,rgba(139,92,246,0.15),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[conic-gradient(from_0deg_at_50%_50%,rgba(99,102,241,0.1),transparent,rgba(139,92,246,0.1))]"></div>
      </div>

      {/* Floating particles */}
      <FloatingParticles />

      <Container className="relative z-10 flex flex-col h-full">
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-16 md:gap-12 items-center min-h-[calc(100vh-120px)]"
          variants={containerVariants}
          initial="hidden"
          animate={controls}
        >
          <motion.div
            className="flex flex-col justify-center space-y-8"
            variants={itemVariants}
          >
            {/* Enhanced badge with glow effect */}
            <motion.div
              variants={itemVariants}
              className="inline-flex"
            >
              <span className="group relative px-4 py-2 text-sm font-medium rounded-full bg-gradient-to-r from-primary-500/20 to-secondary-500/20 text-primary-300 border border-primary-500/30 backdrop-blur-sm hover:border-primary-400/50 transition-all duration-300">
                <span className="relative z-10 flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary-400 rounded-full animate-pulse"></span>
                  Software Engineer
                </span>
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary-500/10 to-secondary-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </span>
            </motion.div>

            {/* Enhanced heading with better typography */}
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
              variants={itemVariants}
            >
              <span className="block text-foreground">Crafting</span>
              <span className="block text-gradient bg-gradient-to-r from-primary-400 via-secondary-400 to-primary-600 bg-clip-text text-transparent">
                Digital Excellence
              </span>
              <span className="block text-3xl md:text-4xl lg:text-5xl text-foreground/70 font-normal mt-2">
                One Line at a Time
              </span>
            </motion.h1>

            {/* Enhanced description */}
            <motion.p
              className="text-xl text-foreground/70 max-w-2xl leading-relaxed"
              variants={itemVariants}
            >
              Full-stack developer with a passion for creating{' '}
              <span className="text-primary-400 font-medium">scalable</span>,{' '}
              <span className="text-secondary-400 font-medium">beautiful</span>, and{' '}
              <span className="text-primary-400 font-medium">user-centered</span> digital experiences
              that make a difference.
            </motion.p>

            {/* Enhanced buttons with better styling */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              variants={itemVariants}
            >
              <Link href="/HarishResume.pdf" target='_blank'>
                <Button className="group relative overflow-hidden bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-500 hover:to-secondary-500 text-white border-0 px-8 py-4 text-lg font-medium transition-all duration-300 transform hover:scale-105" size="lg">
                  <span className="relative z-10 flex items-center gap-2">
                    <Download size={20} />
                    Download Resume
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </Button>
              </Link>
              <Link href="#contact">
                <Button variant="outline" className="group border-2 border-primary-500/50 text-primary-300 hover:border-primary-400 hover:text-white hover:bg-primary-500/10 px-8 py-4 text-lg font-medium transition-all duration-300 transform hover:scale-105" size="lg">
                  <span className="flex items-center gap-2">
                    <Mail size={20} />
                    Let's Talk
                  </span>
                </Button>
              </Link>
            </motion.div>

            {/* Social links */}
            <motion.div
              className="flex items-center gap-4 pt-4"
              variants={itemVariants}
            >
              <span className="text-foreground/60 text-sm">Connect with me:</span>
              <div className="flex gap-3">
                <a href="https://github.com/Harishrawal2" target="_blank" rel="noopener noreferrer" className="p-2 rounded-full bg-foreground/5 hover:bg-foreground/10 text-foreground/60 hover:text-foreground transition-all duration-300 hover:scale-110">
                  <Github size={20} />
                </a>
                <a href="https://www.linkedin.com/in/harish-rawal-b4024b211/" target="_blank" rel="noopener noreferrer" className="p-2 rounded-full bg-foreground/5 hover:bg-foreground/10 text-foreground/60 hover:text-foreground transition-all duration-300 hover:scale-110">
                  <Linkedin size={20} />
                </a>
                <a href="mailto:<EMAIL>" className="p-2 rounded-full bg-foreground/5 hover:bg-foreground/10 text-foreground/60 hover:text-foreground transition-all duration-300 hover:scale-110">
                  <Mail size={20} />
                </a>
              </div>
            </motion.div>
          </motion.div>

          {/* Beautiful CSS-based visualization */}
          <motion.div
            className="relative h-[500px] lg:h-[600px]"
            ref={containerRef}
            variants={itemVariants}
          >
            {/* 3D Laptop with Coding Animation */}
            <div className="absolute inset-0 flex items-center justify-center">
              {/* Background glowing circle */}
              <motion.div
                className="absolute w-64 h-64 rounded-full bg-gradient-to-br from-primary-500/20 to-secondary-500/20 backdrop-blur-xl border border-primary-400/20"
                animate={{
                  rotate: 360,
                }}
                transition={{
                  duration: 30,
                  repeat: Infinity,
                  ease: "linear",
                }}
              >
                {/* Inner glow */}
                <div className="absolute inset-6 rounded-full bg-gradient-to-br from-primary-400/30 to-secondary-400/30 animate-pulse"></div>
              </motion.div>

              {/* 3D Laptop Model */}
              <motion.div
                className="relative z-10"
                initial={{ scale: 0, rotateY: -180 }}
                animate={{ scale: 1, rotateY: 0 }}
                transition={{ duration: 1.5, ease: "backOut" }}
              >
                <div className="relative">
                  {/* Laptop Base */}
                  <motion.div
                    className="w-48 h-32 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg shadow-2xl relative"
                    style={{
                      transform: "perspective(600px) rotateX(60deg)",
                    }}
                    animate={{
                      rotateY: [0, 5, -5, 0],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    {/* Keyboard */}
                    <div className="absolute inset-3 bg-gray-700 rounded grid grid-cols-10 gap-1 p-2">
                      {Array.from({ length: 40 }).map((_, i) => (
                        <motion.div
                          key={i}
                          className="bg-gray-600 rounded-sm h-2"
                          animate={{
                            backgroundColor: [
                              "#4b5563",
                              "#6366f1",
                              "#8b5cf6",
                              "#4b5563",
                            ],
                          }}
                          transition={{
                            duration: 2,
                            repeat: Infinity,
                            delay: i * 0.1,
                            ease: "easeInOut",
                          }}
                        />
                      ))}
                    </div>

                    {/* Trackpad */}
                    <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-12 h-6 bg-gray-600 rounded border border-gray-500"></div>
                  </motion.div>

                  {/* Laptop Screen */}
                  <motion.div
                    className="absolute -top-24 left-0 w-48 h-32 bg-gradient-to-br from-gray-900 to-black rounded-lg shadow-2xl border-2 border-gray-700"
                    style={{
                      transform: "perspective(600px) rotateX(-30deg)",
                      transformOrigin: "bottom",
                    }}
                    animate={{
                      rotateX: [-30, -25, -35, -30],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    {/* Screen Content - Code Editor */}
                    <div className="absolute inset-2 bg-gray-900 rounded overflow-hidden">
                      {/* VS Code Header */}
                      <div className="h-3 bg-gray-800 flex items-center px-2">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                          <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                      </div>

                      {/* Code Lines */}
                      <div className="p-2 space-y-1 text-sm font-mono">
                        {[
                          { text: "const developer = {", color: "#f59e0b" },
                          { text: "  name: 'Harish',", color: "#10b981" },
                          { text: "  skills: ['React',", color: "#3b82f6" },
                          { text: "    'Node.js', 'TS'],", color: "#3b82f6" },
                          { text: "  passion: 'coding'", color: "#8b5cf6" },
                          { text: "};", color: "#f59e0b" },
                        ].map((line, i) => (
                          <motion.div
                            key={i}
                            className="h-2 rounded"
                            style={{ backgroundColor: line.color }}
                            initial={{ width: 0, opacity: 0 }}
                            animate={{ width: "100%", opacity: 1 }}
                            transition={{
                              duration: 0.8,
                              delay: i * 0.3,
                              repeat: Infinity,
                              repeatDelay: 3,
                            }}
                          />
                        ))}
                      </div>

                      {/* Enhanced Attractive Cursor */}
                      <motion.div
                        className="absolute bottom-3 left-3"
                        animate={{
                          opacity: [1, 0, 1],
                        }}
                        transition={{
                          duration: 0.8,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                      >
                        {/* Main cursor line */}
                        <div className="relative">
                          <motion.div
                            className="w-1 h-3 bg-gradient-to-b from-blue-400 to-purple-500 rounded-sm shadow-lg"
                            animate={{
                              scaleY: [1, 1.2, 1],
                              boxShadow: [
                                "0 0 5px rgba(59, 130, 246, 0.5)",
                                "0 0 15px rgba(59, 130, 246, 0.8)",
                                "0 0 5px rgba(59, 130, 246, 0.5)",
                              ],
                            }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                          />

                          {/* Cursor glow effect */}
                          <motion.div
                            className="absolute inset-0 w-1 h-3 bg-blue-400 rounded-sm blur-sm"
                            animate={{
                              opacity: [0.3, 0.8, 0.3],
                              scale: [1, 1.3, 1],
                            }}
                            transition={{
                              duration: 1.5,
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                          />

                          {/* Typing indicator particles */}
                          {Array.from({ length: 3 }).map((_, i) => (
                            <motion.div
                              key={i}
                              className="absolute w-0.5 h-0.5 bg-blue-300 rounded-full"
                              style={{
                                left: `${i * 2 + 4}px`,
                                top: `${Math.random() * 8}px`,
                              }}
                              animate={{
                                opacity: [0, 1, 0],
                                scale: [0, 1, 0],
                                x: [0, 10, 20],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                delay: i * 0.3,
                                ease: "easeOut",
                              }}
                            />
                          ))}
                        </div>
                      </motion.div>
                    </div>

                    {/* Screen Glow */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg animate-pulse"></div>

                    {/* Typing Effect Indicators */}
                    <div className="absolute bottom-2 right-2">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <motion.div
                          key={i}
                          className="inline-block w-1 h-1 bg-green-400 rounded-full mx-0.5"
                          animate={{
                            scale: [0, 1, 0],
                            opacity: [0, 1, 0],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            delay: i * 0.2,
                            ease: "easeInOut",
                          }}
                        />
                      ))}
                    </div>

                    {/* Code Completion Popup */}
                    <motion.div
                      className="absolute bottom-6 right-2 bg-gray-800 rounded px-1 py-0.5 text-xs text-green-400 border border-gray-600"
                      animate={{
                        opacity: [0, 1, 1, 0],
                        y: [10, 0, 0, -10],
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        repeatDelay: 2,
                        ease: "easeInOut",
                      }}
                    >
                      <div className="text-xs">💡 IntelliSense</div>
                    </motion.div>
                  </motion.div>

                  {/* Floating Code Particles */}
                  {Array.from({ length: 6 }).map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute text-xs font-mono text-primary-400/60"
                      style={{
                        left: `${Math.random() * 200 - 100}px`,
                        top: `${Math.random() * 200 - 100}px`,
                      }}
                      animate={{
                        y: [0, -20, 0],
                        opacity: [0, 1, 0],
                        rotate: [0, 360],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: i * 0.5,
                        ease: "easeInOut",
                      }}
                    >
                      {["</>", "{}", "[]", "();", "=>", "&&"][i]}
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Beautiful floating tech icons */}
            <FloatingTechIcons />

            {/* Enhanced decorative elements */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-primary-500/20 to-transparent rounded-full blur-xl animate-pulse"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 bg-gradient-to-br from-secondary-500/20 to-transparent rounded-full blur-xl animate-pulse"></div>

              {/* Additional floating elements */}
              <motion.div
                className="absolute top-1/4 left-1/4 w-16 h-16 bg-gradient-to-br from-primary-400/10 to-transparent rounded-full blur-lg"
                animate={{
                  y: [0, -20, 0],
                  opacity: [0.3, 0.7, 0.3],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />

              <motion.div
                className="absolute bottom-1/4 right-1/4 w-20 h-20 bg-gradient-to-br from-secondary-400/10 to-transparent rounded-full blur-lg"
                animate={{
                  y: [0, 20, 0],
                  opacity: [0.3, 0.7, 0.3],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1,
                }}
              />
            </div>
          </motion.div>
        </motion.div>

        {/* Enhanced scroll indicator */}
        <motion.div
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.5 }}
        >
          <button
            onClick={handleScrollDown}
            className="group flex flex-col items-center text-foreground/60 hover:text-foreground transition-all duration-300 p-4 hover:scale-110"
          >
            <span className="text-sm mb-3 font-medium group-hover:text-primary-400 transition-colors">Explore More</span>
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-b from-primary-400/20 to-secondary-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-150"></div>
              <ArrowDown size={24} className="relative z-10 animate-bounce group-hover:text-primary-400 transition-colors group-hover:animate-pulse" />
            </div>
          </button>
        </motion.div>
      </Container>
    </section>
  );
};

export default Hero;