import React, { useState, useRef } from 'react';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import { Section, SectionTitle } from './ui/Section';
import { Code, Server, Database, Settings, Zap, Sparkles, TrendingUp } from 'lucide-react';

interface SkillItem {
  name: string;
  level: number; // 1-100
  category: string;
}

interface SkillCategory {
  name: string;
  icon: React.ReactNode;
  color: string;
  gradient: string;
  skills: SkillItem[];
  description: string;
}

const skillsData: SkillCategory[] = [
  {
    name: 'Frontend Development',
    icon: <Code size={32} />,
    color: '#3B82F6',
    gradient: 'from-blue-500 via-blue-600 to-cyan-500',
    description: 'Building beautiful, responsive user interfaces',
    skills: [
      { name: 'React', level: 95, category: 'Frontend' },
      { name: 'Next.js', level: 90, category: 'Frontend' },
      { name: 'TypeScript', level: 88, category: 'Frontend' },
      { name: 'Tailwind CSS', level: 92, category: 'Frontend' },
      { name: 'Three.js', level: 75, category: 'Frontend' },
      { name: 'HTML/CSS', level: 95, category: 'Frontend' },
    ],
  },
  {
    name: 'Backend Development',
    icon: <Server size={32} />,
    color: '#10B981',
    gradient: 'from-green-500 via-emerald-600 to-teal-500',
    description: 'Scalable server-side applications and APIs',
    skills: [
      { name: 'Node.js', level: 90, category: 'Backend' },
      { name: 'Express.js', level: 88, category: 'Backend' },
      { name: 'Next.js API', level: 85, category: 'Backend' },
      { name: 'Python', level: 80, category: 'Backend' },
      { name: 'Django', level: 75, category: 'Backend' },
    ],
  },
  {
    name: 'Database & Storage',
    icon: <Database size={32} />,
    color: '#8B5CF6',
    gradient: 'from-purple-500 via-violet-600 to-indigo-500',
    description: 'Data management and optimization',
    skills: [
      { name: 'MongoDB', level: 90, category: 'Database' },
      { name: 'PostgreSQL', level: 85, category: 'Database' },
      { name: 'MySQL', level: 80, category: 'Database' },
      { name: 'Redis', level: 75, category: 'Database' },
      { name: 'Firebase', level: 88, category: 'Database' },
    ],
  },
  {
    name: 'DevOps & Cloud',
    icon: <Settings size={32} />,
    color: '#F59E0B',
    gradient: 'from-orange-500 via-amber-600 to-yellow-500',
    description: 'Deployment and infrastructure management',
    skills: [
      { name: 'Docker', level: 75, category: 'DevOps' },
      { name: 'AWS', level: 70, category: 'DevOps' },
      { name: 'Vercel', level: 90, category: 'DevOps' },
      { name: 'CI/CD', level: 75, category: 'DevOps' },
      { name: 'Netlify', level: 85, category: 'DevOps' },
    ],
  },
];

const SkillBar: React.FC<{ skill: SkillItem; delay: number }> = ({ skill, delay }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, x: -20 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
      transition={{ delay, duration: 0.6 }}
      className="group"
    >
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-foreground/70 group-hover:text-foreground transition-colors">
          {skill.name}
        </span>
        <span className="text-xs text-foreground/60 font-mono">
          {skill.level}%
        </span>
      </div>
      <div className="relative h-2 bg-foreground/10 dark:bg-gray-800 rounded-full overflow-hidden">
        <motion.div
          initial={{ width: 0 }}
          animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
          transition={{ delay: delay + 0.2, duration: 1, ease: "easeOut" }}
          className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full relative"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
        </motion.div>
      </div>
    </motion.div>
  );
};

const Skills: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<number>(0);
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15,
      },
    },
  };

  return (
    <Section id="skills" className="bg-background relative overflow-hidden">
      {/* Enhanced background */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(99,102,241,0.15),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_left,rgba(139,92,246,0.15),transparent_50%)]"></div>
        <div className="absolute top-1/4 right-1/3 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-secondary-500/5 rounded-full blur-3xl"></div>

        {/* Animated grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0)`,
            backgroundSize: '50px 50px'
          }} />
        </div>
      </div>

      <SectionTitle
        title="Skills & Expertise"
        subtitle="Interactive showcase of my technical capabilities and proficiency levels"
        centered
      />

      {/* Category Navigation */}
      <motion.div
        className="flex flex-wrap justify-center gap-4 mb-12"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {skillsData.map((category, index) => (
          <motion.button
            key={category.name}
            variants={itemVariants}
            onClick={() => setActiveCategory(index)}
            className={`group relative px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
              activeCategory === index
                ? 'text-foreground shadow-lg scale-105'
                : 'text-foreground/60 hover:text-foreground hover:scale-105'
            }`}
            style={{
              background: activeCategory === index
                ? `linear-gradient(135deg, ${category.color}40, ${category.color}20)`
                : 'rgba(255, 255, 255, 0.05)',
              border: `1px solid ${activeCategory === index ? category.color + '60' : 'rgba(255, 255, 255, 0.1)'}`
            }}
          >
            <span className="relative z-10 flex items-center gap-2">
              <span style={{ color: category.color }}>
                {category.icon}
              </span>
              {category.name}
            </span>
            {activeCategory === index && (
              <motion.div
                layoutId="activeCategory"
                className="absolute inset-0 rounded-2xl"
                style={{
                  background: `linear-gradient(135deg, ${category.color}20, ${category.color}10)`,
                  border: `1px solid ${category.color}60`
                }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
              />
            )}
          </motion.button>
        ))}
      </motion.div>

      {/* Active Category Display */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeCategory}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className="max-w-6xl mx-auto"
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Category Info */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-6"
            >
              <div className="relative">
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${skillsData[activeCategory].gradient} opacity-10 rounded-3xl blur-xl`}
                />
                <div className="relative bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
                  <div className="flex items-center gap-4 mb-6">
                    <div
                      className="p-4 rounded-2xl"
                      style={{
                        background: `linear-gradient(135deg, ${skillsData[activeCategory].color}40, ${skillsData[activeCategory].color}20)`,
                        border: `1px solid ${skillsData[activeCategory].color}60`
                      }}
                    >
                      <span style={{ color: skillsData[activeCategory].color }}>
                        {skillsData[activeCategory].icon}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-foreground mb-2">
                        {skillsData[activeCategory].name}
                      </h3>
                      <p className="text-foreground/70 text-sm">
                        {skillsData[activeCategory].description}
                      </p>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-white/5 rounded-xl">
                      <div className="text-2xl font-bold text-foreground mb-1">
                        {skillsData[activeCategory].skills.length}
                      </div>
                      <div className="text-xs text-foreground/60">Technologies</div>
                    </div>
                    <div className="text-center p-4 bg-white/5 rounded-xl">
                      <div className="text-2xl font-bold text-foreground mb-1">
                        {Math.round(skillsData[activeCategory].skills.reduce((acc, skill) => acc + skill.level, 0) / skillsData[activeCategory].skills.length)}%
                      </div>
                      <div className="text-xs text-foreground/60">Avg. Proficiency</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Skills Progress Bars */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="space-y-4"
            >
              <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10">
                <div className="flex items-center gap-2 mb-6">
                  <Zap size={20} className="text-primary-400" />
                  <h4 className="text-lg font-semibold text-foreground">Skill Levels</h4>
                </div>
                <div className="space-y-4">
                  {skillsData[activeCategory].skills.map((skill, index) => (
                    <SkillBar
                      key={skill.name}
                      skill={skill}
                      delay={index * 0.1}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Additional Skills Summary */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ delay: 0.6 }}
        className="mt-16 text-center"
      >
        <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-500/20 to-secondary-500/20 rounded-full border border-primary-500/30 backdrop-blur-sm">
          <Sparkles size={16} className="text-primary-400" />
          <span className="text-sm text-foreground/70">
            Continuously learning and expanding my skillset
          </span>
          <TrendingUp size={16} className="text-secondary-400" />
        </div>
      </motion.div>
    </Section>
  );
};

export default Skills;